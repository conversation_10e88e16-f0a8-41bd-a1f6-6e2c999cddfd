#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل البوت مع نظام VIP المتطور
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# إعداد المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_requirements():
    """فحص المتطلبات المطلوبة"""
    required_files = [
        ".env",
        "bot.py", 
        "user_database.py",
        "requirements.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   • {file}")
        return False
    
    # فحص ملف .env
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        required_vars = ["API_ID", "API_HASH", "BOT_TOKEN"]
        missing_vars = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print("❌ متغيرات بيئة مفقودة في ملف .env:")
            for var in missing_vars:
                print(f"   • {var}")
            return False
            
    except ImportError:
        print("❌ مكتبة python-dotenv غير مثبتة")
        print("قم بتشغيل: pip install python-dotenv")
        return False
    
    return True

def display_startup_info():
    """عرض معلومات البدء"""
    print("🤖 بوت سحب المنشورات مع نظام VIP المتطور")
    print("=" * 60)
    print("✨ الميزات الجديدة:")
    print("   💎 نظام VIP متطور مع حساب الوقت التلقائي")
    print("   🎛️ لوحة تحكم شاملة للمشرف")
    print("   📊 إحصائيات مفصلة وتتبع دقيق")
    print("   🔄 فحص دوري للعضويات المنتهية")
    print("   ⚡ تفعيل فوري للعضويات الجديدة")
    print()
    print("🌟 أوامر VIP الجديدة:")
    print("   /vip_panel - لوحة إدارة أعضاء VIP")
    print("   /add_vip [ID] [أيام] - إضافة عضو VIP")
    print("   /remove_vip [ID] - حذف عضو VIP")
    print("   /extend_vip [ID] [أيام] - تمديد اشتراك")
    print()
    print("💰 العضوية المميزة: 1$ شهرياً فقط!")
    print("📞 للاشتراك: @GurusVIP")
    print("=" * 60)

def run_tests():
    """تشغيل اختبارات النظام"""
    print("🧪 تشغيل اختبارات النظام...")
    try:
        from test_vip_system import test_vip_system
        success = test_vip_system()
        if success:
            print("✅ جميع الاختبارات نجحت!")
        return success
    except Exception as e:
        print(f"❌ فشل في الاختبارات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print(f"🚀 بدء تشغيل البوت - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # عرض معلومات البدء
    display_startup_info()
    
    # فحص المتطلبات
    print("🔍 فحص المتطلبات...")
    if not check_requirements():
        print("❌ فشل في فحص المتطلبات!")
        return False
    print("✅ جميع المتطلبات متوفرة!")
    print()
    
    # سؤال المستخدم عن تشغيل الاختبارات
    while True:
        choice = input("هل تريد تشغيل اختبارات النظام أولاً؟ (y/n): ").lower().strip()
        if choice in ['y', 'yes', 'نعم', 'ن']:
            print()
            if not run_tests():
                print("⚠️ فشلت بعض الاختبارات، لكن يمكن المتابعة...")
            print()
            break
        elif choice in ['n', 'no', 'لا', 'ل']:
            break
        else:
            print("❌ اختر y أو n")
    
    # تشغيل البوت
    print("🤖 بدء تشغيل البوت...")
    print("⏳ انتظر قليلاً...")
    print()
    
    try:
        # استيراد وتشغيل البوت
        import bot
        print("✅ تم تحميل البوت بنجاح!")
        print("🎉 البوت يعمل الآن!")
        print()
        print("💡 نصائح:")
        print("   • استخدم /admin للوصول للوحة التحكم")
        print("   • استخدم /vip_panel لإدارة أعضاء VIP")
        print("   • استخدم Ctrl+C لإيقاف البوت")
        print()
        print("📱 البوت جاهز لاستقبال الرسائل!")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
        return True
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("👋 وداعاً!")
        else:
            print("❌ فشل في تشغيل البوت")
            sys.exit(1)
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        sys.exit(1)
