#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام VIP المتطور
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from user_database import user_db, VIPManager
from datetime import datetime, timedelta

# إنشاء مدير VIP للاختبار
vip_manager = VIPManager("test_vip_users.json")

def test_vip_system():
    """اختبار شامل لنظام VIP"""
    print("🧪 بدء اختبار نظام VIP المتطور...")
    print("=" * 50)
    
    # إنشاء مستخدمين تجريبيين
    test_users = [
        {"id": 111111111, "name": "أحمد محمد", "username": "ahmed_test"},
        {"id": 222222222, "name": "سارة علي", "username": "sara_test"},
        {"id": 333333333, "name": "محمد خالد", "username": "mohamed_test"},
        {"id": 444444444, "name": "فاطمة أحمد", "username": "fatima_test"},
        {"id": 555555555, "name": "علي حسن", "username": "ali_test"}
    ]
    
    print("1️⃣ إنشاء مستخدمين تجريبيين...")
    for user in test_users:
        user_db.update_user_info(user["id"], user["username"], user["name"])
        print(f"   ✅ تم إنشاء: {user['name']} ({user['id']})")
    
    print("\n2️⃣ إضافة أعضاء VIP - النظام الجديد...")
    # إضافة أعضاء VIP بفترات مختلفة
    vip_manager.add_vip(111111111, "أحمد محمد", 30)  # شهر
    vip_manager.add_vip(222222222, "سارة علي", 15)   # أسبوعين
    vip_manager.add_vip(333333333, "محمد خالد", 7)   # أسبوع
    vip_manager.add_vip(444444444, "فاطمة أحمد", 3)  # 3 أيام

    # إضافة عضو منتهي الصلاحية (للاختبار)
    vip_manager.add_vip(555555555, "علي حسن", 1)
    # تعديل تاريخ الانتهاء ليكون في الماضي
    vip_data = vip_manager.vip_data
    past_date = datetime.now().date() - timedelta(days=1)
    vip_data["555555555"]["expires_at"] = str(past_date)
    vip_manager._save_vip_data()

    print("   ✅ تم إضافة 5 أعضاء VIP بالنظام الجديد")
    
    print("\n3️⃣ عرض قائمة أعضاء VIP - النظام الجديد...")
    all_vip_users = vip_manager.get_all_vip_users()
    for i, (user_id, user_data) in enumerate(all_vip_users.items(), 1):
        remaining = vip_manager.get_vip_time_remaining(int(user_id))
        status = "✅ نشط" if remaining != 'منتهية' else "❌ منتهي"
        print(f"   {i}. {status} {user_data['name']} - {remaining}")

    print("\n4️⃣ اختبار فحص العضويات المنتهية - النظام الجديد...")
    expired_count = vip_manager.check_and_remove_expired()
    print(f"   🔄 تم حذف {expired_count} عضوية منتهية")

    print("\n5️⃣ اختبار تمديد العضوية - النظام الجديد...")
    vip_manager.extend_vip(222222222, 10)  # تمديد 10 أيام
    updated_info = vip_manager.get_vip_info(222222222)
    remaining = vip_manager.get_vip_time_remaining(222222222)
    print(f"   ✅ تم تمديد عضوية سارة علي - المتبقي: {remaining}")
    
    print("\n6️⃣ اختبار حدود الاستخدام...")
    # اختبار مستخدم مجاني
    free_user_id = 666666666
    user_db.update_user_info(free_user_id, "free_user", "مستخدم مجاني")
    
    # محاولة تجاوز الحد (50 تحويل)
    for i in range(52):
        success = user_db.increment_downloads(free_user_id)
        if not success:
            print(f"   🚫 تم منع التحويل رقم {i+1} - تجاوز الحد المسموح")
            break
    
    limits = user_db.get_user_limits(free_user_id)
    print(f"   📊 حدود المستخدم المجاني: {limits['used_today']}/{limits['daily_limit']}")
    
    print("\n7️⃣ اختبار مستخدم VIP (بدون حدود) - النظام الجديد...")
    vip_user_id = 111111111
    for i in range(100):  # اختبار 100 تحويل
        success = user_db.increment_downloads(vip_user_id, vip_manager)
        if not success:
            print(f"   ❌ فشل في التحويل رقم {i+1}")
            break

    vip_user_data = user_db.get_user(vip_user_id)
    print(f"   ✅ مستخدم VIP - التحويلات: {vip_user_data['daily_downloads']} (غير محدود)")

    print("\n8️⃣ عرض الإحصائيات النهائية - النظام الجديد...")
    stats = user_db.get_stats()
    final_vip_users = vip_manager.get_all_vip_users()
    active_vips = len([u_id for u_id, u in final_vip_users.items() if vip_manager.get_vip_time_remaining(int(u_id)) != 'منتهية'])

    print(f"   📊 إجمالي المستخدمين: {stats['total_users']}")
    print(f"   💎 أعضاء VIP النشطين: {active_vips}")
    print(f"   📈 إجمالي التحويلات: {stats['total_downloads']}")
    
    print("\n" + "=" * 50)
    print("✅ تم اكتمال اختبار نظام VIP بنجاح!")
    print("🎉 جميع الميزات تعمل بشكل صحيح!")
    
    return True

def display_vip_panel():
    """عرض لوحة VIP كما تظهر في البوت - النظام الجديد"""
    print("\n" + "=" * 50)
    print("💎 لوحة إدارة أعضاء VIP - النظام الجديد")
    print("=" * 50)

    # فحص العضويات المنتهية
    expired_count = vip_manager.check_and_remove_expired()
    all_vip_users = vip_manager.get_all_vip_users()

    print(f"\n📊 الإحصائيات:")
    print(f"• إجمالي الأعضاء: {len(all_vip_users)}")
    active_count = len([u_id for u_id, u in all_vip_users.items() if vip_manager.get_vip_time_remaining(int(u_id)) != 'منتهية'])
    print(f"• الأعضاء النشطين: {active_count}")
    print(f"• الاشتراكات المنتهية: {expired_count}")
    print(f"\n🔄 تم فحص وحذف {expired_count} عضوية منتهية")

    print(f"\n👥 قائمة أعضاء VIP:")
    if not all_vip_users:
        print("لا يوجد أعضاء VIP حالياً")
    else:
        for i, (user_id, user_data) in enumerate(list(all_vip_users.items())[:10], 1):
            remaining = vip_manager.get_vip_time_remaining(int(user_id))
            status = "✅" if remaining != 'منتهية' else "❌"
            name = user_data['name'][:15] + "..." if len(user_data['name']) > 15 else user_data['name']
            print(f"{i}. {status} {name} ({remaining})")

        if len(all_vip_users) > 10:
            print(f"... و {len(all_vip_users) - 10} عضو آخر")

    print(f"\n🛠️ أوامر الإدارة:")
    print("• /add_vip [ID] [أيام] [اسم] - إضافة عضو VIP")
    print("• /remove_vip [ID] - حذف عضو VIP")
    print("• /extend_vip [ID] [أيام] - تمديد اشتراك")
    print("• /vip_list - قائمة مفصلة بالأعضاء")

if __name__ == "__main__":
    try:
        # تشغيل الاختبار
        test_vip_system()
        
        # عرض لوحة VIP
        display_vip_panel()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
