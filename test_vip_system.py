#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام VIP المتطور
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from user_database import user_db
from datetime import datetime, timed<PERSON><PERSON>

def test_vip_system():
    """اختبار شامل لنظام VIP"""
    print("🧪 بدء اختبار نظام VIP المتطور...")
    print("=" * 50)
    
    # إنشاء مستخدمين تجريبيين
    test_users = [
        {"id": 111111111, "name": "أحمد محمد", "username": "ahmed_test"},
        {"id": 222222222, "name": "سارة علي", "username": "sara_test"},
        {"id": 333333333, "name": "محمد خالد", "username": "mohamed_test"},
        {"id": 444444444, "name": "فاطمة أحمد", "username": "fatima_test"},
        {"id": 555555555, "name": "علي حسن", "username": "ali_test"}
    ]
    
    print("1️⃣ إنشاء مستخدمين تجريبيين...")
    for user in test_users:
        user_db.update_user_info(user["id"], user["username"], user["name"])
        print(f"   ✅ تم إنشاء: {user['name']} ({user['id']})")
    
    print("\n2️⃣ إضافة أعضاء VIP...")
    # إضافة أعضاء VIP بفترات مختلفة
    user_db.upgrade_to_premium(111111111, 30)  # شهر
    user_db.upgrade_to_premium(222222222, 15)  # أسبوعين
    user_db.upgrade_to_premium(333333333, 7)   # أسبوع
    user_db.upgrade_to_premium(444444444, 3)   # 3 أيام
    
    # إضافة عضو منتهي الصلاحية (للاختبار)
    user_db.upgrade_to_premium(555555555, 1)
    expired_user = user_db.get_user(555555555)
    # تعديل تاريخ الانتهاء ليكون في الماضي
    past_date = datetime.now() - timedelta(days=1)
    expired_user["premium_expiry"] = past_date.strftime("%Y-%m-%d %H:%M:%S")
    user_db._save_database()
    
    print("   ✅ تم إضافة 5 أعضاء VIP")
    
    print("\n3️⃣ عرض قائمة أعضاء VIP...")
    premium_users = user_db.get_all_premium_users()
    for i, user in enumerate(premium_users, 1):
        status = "✅ نشط" if user['remaining_time'] != 'منتهية' else "❌ منتهي"
        print(f"   {i}. {status} {user['first_name']} - {user['remaining_time']}")
    
    print("\n4️⃣ اختبار فحص العضويات المنتهية...")
    expired_count = user_db.check_all_expired_memberships()
    print(f"   🔄 تم إلغاء {expired_count} عضوية منتهية")
    
    print("\n5️⃣ اختبار تمديد العضوية...")
    user_db.extend_premium(222222222, 10)  # تمديد 10 أيام
    updated_user = user_db.get_user(222222222)
    remaining = user_db.get_premium_time_remaining(222222222)
    print(f"   ✅ تم تمديد عضوية سارة علي - المتبقي: {remaining}")
    
    print("\n6️⃣ اختبار حدود الاستخدام...")
    # اختبار مستخدم مجاني
    free_user_id = 666666666
    user_db.update_user_info(free_user_id, "free_user", "مستخدم مجاني")
    
    # محاولة تجاوز الحد (50 تحويل)
    for i in range(52):
        success = user_db.increment_downloads(free_user_id)
        if not success:
            print(f"   🚫 تم منع التحويل رقم {i+1} - تجاوز الحد المسموح")
            break
    
    limits = user_db.get_user_limits(free_user_id)
    print(f"   📊 حدود المستخدم المجاني: {limits['used_today']}/{limits['daily_limit']}")
    
    print("\n7️⃣ اختبار مستخدم VIP (بدون حدود)...")
    vip_user_id = 111111111
    for i in range(100):  # اختبار 100 تحويل
        success = user_db.increment_downloads(vip_user_id)
        if not success:
            print(f"   ❌ فشل في التحويل رقم {i+1}")
            break
    
    vip_limits = user_db.get_user_limits(vip_user_id)
    print(f"   ✅ مستخدم VIP - التحويلات: {vip_limits['used_today']} (غير محدود)")
    
    print("\n8️⃣ عرض الإحصائيات النهائية...")
    stats = user_db.get_stats()
    final_premium_users = user_db.get_all_premium_users()
    active_vips = len([u for u in final_premium_users if u['remaining_time'] != 'منتهية'])
    
    print(f"   📊 إجمالي المستخدمين: {stats['total_users']}")
    print(f"   💎 أعضاء VIP النشطين: {active_vips}")
    print(f"   📈 إجمالي التحويلات: {stats['total_downloads']}")
    
    print("\n" + "=" * 50)
    print("✅ تم اكتمال اختبار نظام VIP بنجاح!")
    print("🎉 جميع الميزات تعمل بشكل صحيح!")
    
    return True

def display_vip_panel():
    """عرض لوحة VIP كما تظهر في البوت"""
    print("\n" + "=" * 50)
    print("💎 لوحة إدارة أعضاء VIP")
    print("=" * 50)
    
    # فحص العضويات المنتهية
    expired_count = user_db.check_all_expired_memberships()
    premium_users = user_db.get_all_premium_users()
    
    print(f"\n📊 الإحصائيات:")
    print(f"• إجمالي الأعضاء: {len(premium_users)}")
    print(f"• الأعضاء النشطين: {len([u for u in premium_users if u['remaining_time'] != 'منتهية'])}")
    print(f"• الاشتراكات المنتهية: {len([u for u in premium_users if u['remaining_time'] == 'منتهية'])}")
    print(f"\n🔄 تم فحص وإلغاء {expired_count} عضوية منتهية")
    
    print(f"\n👥 قائمة أعضاء VIP:")
    if not premium_users:
        print("لا يوجد أعضاء VIP حالياً")
    else:
        for i, user in enumerate(premium_users[:10], 1):
            status = "✅" if user['remaining_time'] != 'منتهية' else "❌"
            name = user['first_name'][:15] + "..." if len(user['first_name']) > 15 else user['first_name']
            print(f"{i}. {status} {name} ({user['remaining_time']})")
        
        if len(premium_users) > 10:
            print(f"... و {len(premium_users) - 10} عضو آخر")
    
    print(f"\n🛠️ أوامر الإدارة:")
    print("• /add_vip [ID] [أيام] - إضافة عضو VIP")
    print("• /remove_vip [ID] - حذف عضو VIP") 
    print("• /extend_vip [ID] [أيام] - تمديد اشتراك")
    print("• /vip_panel - عرض هذه اللوحة")

if __name__ == "__main__":
    try:
        # تشغيل الاختبار
        test_vip_system()
        
        # عرض لوحة VIP
        display_vip_panel()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
