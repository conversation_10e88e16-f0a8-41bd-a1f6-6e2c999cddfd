from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes
import requests
from bs4 import BeautifulSoup
import logging
import json
from datetime import datetime, timezone, timedelta
import asyncio
import bleach
import re
from apscheduler.schedulers.background import BackgroundScheduler
from dotenv import load_dotenv
import os
import time

from functools import lru_cache

from typing import Dict, Optional
from discudemy_scraper import DiscudemyScraper
from real_discount import RealDiscountScraper

load_dotenv()
# قراءة المتغيرات من ملف .env
TOKEN = os.getenv("TOKEN")
CHANNEL_ID = os.getenv("CHANNEL_ID")
CHANNEL_USERNAME = os.getenv("CHANNEL_USERNAME")
CHANNEL_URL = os.getenv("CHANNEL_URL")

def check_environment_variables():
    if not TOKEN or not CHANNEL_ID or not CHANNEL_USERNAME or not CHANNEL_URL:
        raise EnvironmentError("المتغيرات البيئية الأساسية غير موجودة في ملف .env")
    logger.info("تم تحميل التكوين من ملف .env بنجاح")
    logger.info(f"معرف القناة: {CHANNEL_ID}")
    logger.info(f"اسم المستخدم للقناة: {CHANNEL_USERNAME}")
    logger.info(f"رابط القناة: {CHANNEL_URL}")
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

ADMIN_IDS_STR = os.getenv("ADMIN_IDS", "1789531376")
ADMIN_IDS = [int(id.strip()) for id in ADMIN_IDS_STR.split(",")]

VIP_FILE = "vip_users.json"

# تم إلغاء نظام اختصار الروابط

# قائمة الدورات المحظورة (لن يتم عرضها في البوت)
BLOCKED_COURSES = [
    "https://www.udemy.com/course/print-on-demand-free/",
    "print-on-demand-free"  # اسم الدورة في الرابط
]

# إعدادات الإشعارات
AUTO_CHECK_NOTIFICATIONS = True  # تفعيل/إيقاف إشعارات الفحص التلقائي

# نظام الحماية من التضارب
import threading
_update_lock = threading.Lock()  # قفل لحماية عمليات التحديث

# نظام إدارة العمليات المتزامنة للمستخدمين
class UserOperationManager:
    """مدير العمليات المتزامنة للمستخدمين"""

    def __init__(self):
        self.user_operations = {}  # {user_id: {'operation': 'update_courses', 'start_time': datetime, 'message_id': int}}
        self.operation_lock = threading.Lock()

    def start_operation(self, user_id: int, operation: str, message_id: int = None) -> bool:
        """بدء عملية جديدة للمستخدم"""
        with self.operation_lock:
            if user_id in self.user_operations:
                # المستخدم لديه عملية قيد التشغيل
                return False

            self.user_operations[user_id] = {
                'operation': operation,
                'start_time': datetime.now(),
                'message_id': message_id
            }
            return True

    def end_operation(self, user_id: int) -> bool:
        """إنهاء العملية للمستخدم"""
        with self.operation_lock:
            if user_id in self.user_operations:
                del self.user_operations[user_id]
                return True
            return False

    def get_user_operation(self, user_id: int) -> dict:
        """الحصول على العملية الحالية للمستخدم"""
        with self.operation_lock:
            return self.user_operations.get(user_id, None)

    def is_user_busy(self, user_id: int) -> bool:
        """التحقق من كون المستخدم مشغول بعملية أخرى"""
        with self.operation_lock:
            return user_id in self.user_operations

    def get_operation_duration(self, user_id: int) -> int:
        """الحصول على مدة العملية بالثواني"""
        operation = self.get_user_operation(user_id)
        if operation:
            return int((datetime.now() - operation['start_time']).total_seconds())
        return 0

# إنشاء نسخة عامة من مدير العمليات
user_operation_manager = UserOperationManager()

class VIPManager:
    def __init__(self):
        self.vip_data: Dict = self._load_vip_data()

    def _load_vip_data(self) -> Dict:
        """تحميل بيانات المستخدمين VIP من الملف"""
        try:
            if os.path.exists(VIP_FILE):
                with open(VIP_FILE, "r", encoding="utf-8") as f:
                    return json.load(f)
            # إنشاء ملف فارغ إذا لم يكن موجودًا
            with open(VIP_FILE, "w", encoding="utf-8") as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            return {}
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات VIP: {e}")
            return {}

    def _save_vip_data(self) -> bool:
        """حفظ بيانات المستخدمين VIP إلى الملف"""
        try:
            with open(VIP_FILE, "w", encoding="utf-8") as f:
                json.dump(self.vip_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات VIP: {e}")
            return False

    def is_vip(self, user_id: int) -> bool:
        """التحقق من حالة VIP للمستخدم"""
        try:
            # المشرفون هم VIP تلقائيًا
            if is_admin(user_id):
                return True

            user = self.vip_data.get(str(user_id))
            if not user:
                return False

            expiry = datetime.strptime(user["expires_at"], "%Y-%m-%d").date()
            return expiry >= datetime.now().date()
        except Exception as e:
            logger.error(f"خطأ في التحقق من حالة VIP للمستخدم {user_id}: {e}")
            return False

    def add_vip(self, user_id: int, name: str, days: int) -> bool:
        """إضافة مستخدم جديد كـ VIP"""
        try:
            joined_at = datetime.now().date()
            expires_at = joined_at + timedelta(days=days)

            self.vip_data[str(user_id)] = {
                "name": name,
                "joined_at": str(joined_at),
                "expires_at": str(expires_at),
                "days_total": days
            }

            return self._save_vip_data()
        except Exception as e:
            logger.error(f"خطأ في إضافة مستخدم VIP {user_id}: {e}")
            return False

    def remove_vip(self, user_id: int) -> bool:
        """حذف مستخدم VIP"""
        try:
            if str(user_id) in self.vip_data:
                del self.vip_data[str(user_id)]
                return self._save_vip_data()
            return False
        except Exception as e:
            logger.error(f"خطأ في حذف مستخدم VIP {user_id}: {e}")
            return False

    def get_vip_info(self, user_id: int) -> Optional[Dict]:
        """الحصول على معلومات VIP للمستخدم"""
        return self.vip_data.get(str(user_id))

    def get_all_vip_users(self) -> Dict:
        """الحصول على جميع مستخدمي VIP"""
        return self.vip_data

# إنشاء نسخة عامة من مدير VIP
vip_manager = VIPManager()

class UpdateLimitManager:
    """مدير حدود التحديث للمستخدمين العاديين"""

    def __init__(self):
        self.limits_file = "update_limits.json"
        self.daily_limit = 3  # 3 مرات يومياً
        self.limits_data = self._load_limits_data()

    def _load_limits_data(self) -> dict:
        """تحميل بيانات حدود التحديث"""
        try:
            if os.path.exists(self.limits_file):
                with open(self.limits_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات حدود التحديث: {e}")
            return {}

    def _save_limits_data(self) -> bool:
        """حفظ بيانات حدود التحديث"""
        try:
            with open(self.limits_file, 'w', encoding='utf-8') as f:
                json.dump(self.limits_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات حدود التحديث: {e}")
            return False

    def can_update(self, user_id: int) -> tuple[bool, int]:
        """التحقق من إمكانية التحديث وإرجاع عدد المرات المتبقية"""
        today = datetime.now().date().isoformat()
        user_data = self.limits_data.get(str(user_id), {})

        # إذا كان اليوم مختلف، إعادة تعيين العداد
        if user_data.get('date') != today:
            user_data = {'date': today, 'count': 0}
            self.limits_data[str(user_id)] = user_data

        remaining = self.daily_limit - user_data.get('count', 0)
        return remaining > 0, remaining

    def use_update(self, user_id: int) -> bool:
        """استخدام مرة تحديث واحدة"""
        can_update, _ = self.can_update(user_id)
        if not can_update:
            return False

        today = datetime.now().date().isoformat()
        user_data = self.limits_data.get(str(user_id), {'date': today, 'count': 0})
        user_data['count'] = user_data.get('count', 0) + 1
        user_data['date'] = today

        self.limits_data[str(user_id)] = user_data
        return self._save_limits_data()

    def get_reset_time(self) -> str:
        """الحصول على وقت إعادة تعيين الحد اليومي"""
        tomorrow = datetime.now().date() + timedelta(days=1)
        return tomorrow.isoformat()

# إنشاء نسخة عامة من مدير حدود التحديث
update_limit_manager = UpdateLimitManager()

def is_admin(user_id: int) -> bool:
    """التحقق مما إذا كان المستخدم مشرفًا"""
    return user_id in ADMIN_IDS

def is_course_blocked(course_link: str, course_title: str = "") -> bool:
    """التحقق من كون الدورة محظورة"""
    if not course_link:
        return False

    # التحقق من الرابط المباشر
    for blocked_item in BLOCKED_COURSES:
        if blocked_item in course_link.lower():
            return True

    # التحقق من العنوان إذا كان متوفراً
    if course_title:
        course_title_lower = course_title.lower()
        if "print on demand" in course_title_lower and "free" in course_title_lower:
            return True

    return False

async def send_waiting_message(update: Update, operation_name: str, user_id: int) -> object:
    """إرسال رسالة انتظار للمستخدم"""
    waiting_messages = {
        'update_courses': [
            "⏳ جاري تحديث الكورسات، يرجى الانتظار...",
            "🔄 جاري البحث عن أحدث الكورسات المجانية...",
            "⏰ جاري فحص المواقع للعثور على كورسات جديدة...",
            "🔍 جاري تحديث قاعدة البيانات بأحدث الكورسات...",
            "📚 جاري جلب الكورسات الجديدة، يرجى الصبر..."
        ],
        'show_courses': [
            "⏳ جاري تحضير قائمة الكورسات...",
            "📋 جاري تحميل الكورسات المتاحة...",
            "🔄 جاري عرض أحدث الكورسات المجانية...",
            "📚 جاري تجهيز الكورسات لك...",
            "⏰ جاري تحضير قائمة الكورسات المتاحة..."
        ],
        'general': [
            "⏳ جاري المعالجة، يرجى الانتظار...",
            "🔄 جاري تنفيذ العملية...",
            "⏰ يرجى الانتظار قليلاً...",
            "🔍 جاري المعالجة..."
        ]
    }

    import random
    messages = waiting_messages.get(operation_name, waiting_messages['general'])
    selected_message = random.choice(messages)

    # إضافة معرف المستخدم للرسالة
    user_info = f"\n\n👤 معرف المستخدم: {user_id}"
    selected_message += user_info

    try:
        if hasattr(update, 'callback_query') and update.callback_query:
            message = await update.callback_query.message.reply_text(selected_message)
        else:
            message = await update.message.reply_text(selected_message)
        return message
    except Exception as e:
        logger.error(f"خطأ في إرسال رسالة الانتظار: {e}")
        return None

async def update_waiting_message(message, new_text: str, user_id: int):
    """تحديث رسالة الانتظار"""
    try:
        if message:
            user_info = f"\n\n👤 معرف المستخدم: {user_id}"
            await message.edit_text(new_text + user_info)
    except Exception as e:
        logger.error(f"خطأ في تحديث رسالة الانتظار: {e}")

async def check_user_operation_conflict(update: Update, _operation_name: str) -> bool:
    """التحقق من تضارب العمليات للمستخدم"""
    user_id = update.effective_user.id

    if user_operation_manager.is_user_busy(user_id):
        current_operation = user_operation_manager.get_user_operation(user_id)
        duration = user_operation_manager.get_operation_duration(user_id)

        conflict_message = (
            f"⚠️ لديك عملية أخرى قيد التشغيل!\n\n"
            f"🔄 العملية الحالية: {current_operation['operation']}\n"
            f"⏱️ مدة التشغيل: {duration} ثانية\n"
            f"👤 معرف المستخدم: {user_id}\n\n"
            f"يرجى انتظار انتهاء العملية الحالية قبل بدء عملية جديدة."
        )

        try:
            if hasattr(update, 'callback_query') and update.callback_query:
                await update.callback_query.message.reply_text(conflict_message)
            else:
                await update.message.reply_text(conflict_message)
        except Exception as e:
            logger.error(f"خطأ في إرسال رسالة التضارب: {e}")

        return True

    return False

# تم إلغاء نظام اختصار الروابط - البوت للمشرفين و VIP فقط

# نظام التحكم في التوقيت بين التحديثات (صارم وشامل - 3 دقائق)
class UpdateCooldownManager:
    def __init__(self):
        self.last_update_time = None  # وقت واحد لجميع التحديثات
        self.cooldown_minutes = 3  # 3 دقائق بين أي تحديث

    def can_update(self) -> tuple[bool, int]:
        """التحقق من إمكانية أي تحديث وإرجاع الوقت المتبقي بالثواني"""
        current_time = datetime.now()

        if self.last_update_time is None:
            return True, 0

        time_diff = current_time - self.last_update_time
        cooldown_seconds = self.cooldown_minutes * 60

        if time_diff.total_seconds() >= cooldown_seconds:
            return True, 0
        else:
            remaining_seconds = cooldown_seconds - int(time_diff.total_seconds())
            return False, remaining_seconds

    def record_update(self):
        """تسجيل وقت التحديث (يطبق على جميع التحديثات)"""
        self.last_update_time = datetime.now()

    def get_remaining_time_text(self, remaining_seconds: int) -> str:
        """تحويل الثواني المتبقية إلى نص مقروء"""
        minutes = remaining_seconds // 60
        seconds = remaining_seconds % 60

        if minutes > 0:
            return f"{minutes} دقيقة و {seconds} ثانية"
        else:
            return f"{seconds} ثانية"

    def get_last_update_info(self) -> str:
        """الحصول على معلومات آخر تحديث"""
        if self.last_update_time is None:
            return "لم يتم تسجيل أي تحديث بعد"

        time_diff = datetime.now() - self.last_update_time
        minutes_passed = int(time_diff.total_seconds() // 60)

        if minutes_passed < 1:
            return "منذ أقل من دقيقة"
        elif minutes_passed == 1:
            return "منذ دقيقة واحدة"
        else:
            return f"منذ {minutes_passed} دقيقة"

# إنشاء مدير التحكم في التوقيت
update_cooldown_manager = UpdateCooldownManager()

def is_authorized_user(user_id: int) -> bool:
    """التحقق من كون المستخدم مخول لاستخدام البوت (مشرف أو VIP)"""
    return is_admin(user_id) or vip_manager.is_vip(user_id)

async def check_user_authorization(update: Update) -> bool:
    """التحقق من صلاحية المستخدم وإرسال رسالة إذا لم يكن مخولاً"""
    user_id = update.effective_user.id

    if not is_authorized_user(user_id):
        unauthorized_message = (
    "╔══════ *🌟 نظام العضوية المميزة* 🌟 ══════╗\n\n"
    "🔒 *مرحباً بك في المحتوى الحصري!*\n\n"
    "💎 *عرض خاص: اشترك في VIP مقابل 1$ شهرياً فقط!*\n\n"
    "*✨ المزايا الحصرية للأعضاء المميزين:*\n"
    "◈ 🎯 وصول فوري لجميع الدورات المجانية\n"
    "◈ 🚀 تحديثات لحظية لأحدث العروض\n"
    "◈ 💫 روابط مباشرة بدون إعلانات\n"
    "◈ 🛡️ دعم فني متميز على مدار الساعة\n"
    "◈ 🎨 واجهة مخصصة سهلة الاستخدام\n"
    "◈ 🔥 محتوى حصري للأعضاء المميزين\n\n"
    "*💰 العرض الحصري:*\n"
    "◈ 📌 سعر الاشتراك: 1$ شهرياً\n"
    "◈ 🎁 هدايا حصرية للمشتركين الجدد\n\n"
    "*📢 خيار مجاني متاح!*\n"
    "◈ 📱 تابع قناتنا المجانية للكورسات\n"
    "◈ 🔄 روابط مختصرة لدعم خدماتنا\n"
    "◈ 📚 محتوى تعليمي منظم ومفيد\n\n"
    "*⭐️ احصل على تجربة VIP الآن!*\n"
    "◈ 🔓 روابط مباشرة بدون إعلانات\n"
    "◈ 🎓 وصول غير محدود للدورات\n"
    "◈ 💫 ميزات حصرية إضافية"
)

        keyboard = [
            [InlineKeyboardButton("💎 اشترك الآن", url="https://t.me/GurusVIP")],
            [InlineKeyboardButton("📢 قناة الدورات", url=f"https://t.me/{CHANNEL_USERNAME}")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            unauthorized_message,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
        return False

    return True

async def admin_only(update: Update, _context: ContextTypes.DEFAULT_TYPE) -> bool:
    """التحقق من صلاحيات المشرف وإرسال رسالة خطأ إذا لم يكن مشرفًا"""
    if not is_admin(update.effective_user.id):
        await update.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        return False
    return True

class Config:
    BASE_URL = 'https://comidoc.net'
    DISCUDEMY_URL = 'https://www.discudemy.com/language/arabic'
    REAL_DISCOUNT_URL = 'https://cdn.real.discount/api/courses'
    REQUEST_TIMEOUT = 10
    COURSES_FILE = 'courses.json'
    EN_COURSES_FILE = 'en_courses.json'
    DISCUDEMY_COURSES_FILE = 'discudemy_courses.json'
    REAL_DISCOUNT_COURSES_FILE = 'real_discount_courses.json'
    SENT_COURSES_FILE = 'sent_courses.json'
    MAX_PAGES = 66  # تقليل عدد الصفحات المعالجة
    DISCUDEMY_MAX_PAGES = 20  # عدد صفحات discudemy للمعالجة
    CONCURRENT_REQUESTS = 10  # عدد الطلبات المتزامنة
    CACHE_TIMEOUT = 3600  # مدة صلاحية التخزين المؤقت بالثواني (ساعة واحدة)

def initialize_files():
    """إنشاء الملفات المطلوبة إذا لم تكن موجودة"""
    files = {
        Config.COURSES_FILE: [],
        Config.EN_COURSES_FILE: [],
        Config.DISCUDEMY_COURSES_FILE: [],
        Config.REAL_DISCOUNT_COURSES_FILE: [],
        Config.SENT_COURSES_FILE: [],
    }

    for file_path, default_content in files.items():
        if not os.path.exists(file_path):
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(default_content, f, ensure_ascii=False, indent=4)
                logger.info(f"تم إنشاء ملف {file_path}")
            except Exception as e:
                logger.error(f"خطأ في إنشاء ملف {file_path}: {e}")

# إعداد المجدول
scheduler = BackgroundScheduler({'apscheduler.timezone': 'UTC'})

# إعداد جلسة الطلبات
session = requests.Session()
session.headers.update({'User-Agent': 'Mozilla/5.0 (compatible; CourseBot/1.0)'})

# نظام التخزين المؤقت
cache = {}

def get_cached_data(key, fetch_func, timeout=Config.CACHE_TIMEOUT):
    """الحصول على البيانات من التخزين المؤقت أو جلبها إذا لم تكن موجودة"""
    current_time = time.time()
    if key in cache and current_time - cache[key]['timestamp'] < timeout:
        return cache[key]['data']

    # جلب البيانات
    data = fetch_func()

    # تخزين البيانات في التخزين المؤقت
    cache[key] = {
        'data': data,
        'timestamp': current_time
    }

    return data

# تخزين مؤقت للطلبات HTTP
@lru_cache(maxsize=100)
def cached_request(url, timeout=Config.REQUEST_TIMEOUT):
    """إجراء طلب HTTP مع تخزين مؤقت"""
    response = session.get(url, timeout=timeout)
    response.raise_for_status()
    return response.text

def check_course_coupon(course_url):
    """التحقق من حالة الكوبون في صفحة الدورة"""
    try:
        # التحقق من صحة الرابط
        if not course_url.startswith(('http://', 'https://')):
            return "error"

        # استخدام التخزين المؤقت للطلبات
        html_content = cached_request(course_url)

        # تحليل HTML بشكل أكثر كفاءة
        soup = BeautifulSoup(html_content, 'html.parser')

        # البحث عن جميع النصوص المتعلقة بالكوبونات في مرة واحدة
        all_text = ' '.join([element.get_text() for element in soup.select('div, span, p, h1, h2, h3, h4, h5, h6')])
        all_text_upper = all_text.upper()

        # التحقق من الكوبون المجاني
        if '100%OFF COUPON' in all_text_upper or '100% OFF' in all_text_upper:
            return "paid_with_coupon"

        # التحقق من الكوبون المخفض
        if 'COUPON' in all_text_upper and '$' in all_text and not ('100%' in all_text_upper and 'OFF' in all_text_upper):
            return "discounted"

        # التحقق من الخصومات الجزئية
        if re.search(r'\d+%\s*OFF', all_text_upper) and not re.search(r'100%\s*OFF', all_text_upper):
            return "discounted"

        # إذا لم يتم العثور على أي خصم، فالكوبون منتهي الصلاحية
        return "expired"

    except requests.exceptions.RequestException as e:
        logger.error(f"خطأ في الاتصال: {e}")
        return "error"
    except Exception as e:
        logger.error(f"خطأ غير متوقع: {e}")
        return "error"

def extract_udemy_link_and_coupons_left(course_url):
    """استخراج رابط Udemy مع كود الخصم وعدد الكوبونات المتبقية والسعر الأصلي من صفحة الدورة"""
    try:
        # استخدام التخزين المؤقت للطلبات
        html_content = cached_request(course_url)
        soup = BeautifulSoup(html_content, 'html.parser')

        # البحث عن رابط Udemy بشكل أكثر كفاءة
        udemy_link = soup.select_one('a[href*="udemy.com/course"][href*="couponCode"]')

        # البحث عن عدد الكوبونات المتبقية بطريقة أكثر كفاءة
        coupons_left = 0

        # جمع كل النصوص التي تحتوي على "left" أو "remaining"
        left_texts = []
        for element in soup.select('div, span, p, strong'):
            text = element.get_text().lower().strip()
            if 'left' in text or 'remaining' in text:
                left_texts.append(text)

        # البحث عن نمط الكوبونات المتبقية في النصوص المجمعة
        for text in left_texts:
            # تحديد النص الذي يحتوي على "left" وأرقام قبله
            match = re.search(r'(\d+)\s*(?:coupons?|codes?)?(?:\s+left|\s+remaining)', text)
            if match:
                coupons_left = int(match.group(1))
                # التحقق من أن الرقم منطقي (أقل من 10000)
                if coupons_left > 0 and coupons_left < 10000:
                    break

        # إذا لم نجد عدد الكوبونات، نبحث عن أي رقم قريب من كلمة "left"
        if coupons_left == 0:
            for text in left_texts:
                # استخراج جميع الأرقام من النص
                numbers = re.findall(r'\d+', text)
                for num in numbers:
                    if len(num) < 5:  # نتأكد أن الرقم ليس كبيرًا جدًا
                        coupons_left = int(num)
                        if coupons_left > 0:
                            break
                if coupons_left > 0:
                    break

        # البحث عن نص عربي يحتوي على "متبقي" أو "متبقية"
        if coupons_left == 0:
            arabic_texts = []
            for element in soup.select('div, span, p, strong'):
                text = element.get_text().strip()
                if 'متبقي' in text or 'متبقية' in text:
                    arabic_texts.append(text)

            for text in arabic_texts:
                # استخراج الأرقام من النص العربي
                numbers = re.findall(r'\d+', text)
                for num in numbers:
                    if len(num) < 5:
                        coupons_left = int(num)
                        if coupons_left > 0:
                            break
                if coupons_left > 0:
                    break

        # إذا لم نجد عدد الكوبونات، نفترض أن هناك 100 كوبون متبقي
        if coupons_left == 0 or coupons_left > 10000:
            coupons_left = 100

        # استخراج صورة الدورة في نفس الوظيفة
        thumbnail = 'https://via.placeholder.com/150'  # صورة افتراضية
        picture = soup.find('picture')

        if picture:
            img = picture.find('img')
            if img and img.get('src'):
                thumbnail = img['src']
            else:
                source = picture.find('source', type='image/jpeg')
                if source and source.get('srcset'):
                    thumbnail = source['srcset']

        # البحث عن صورة في أماكن أخرى إذا لم نجدها
        if thumbnail == 'https://via.placeholder.com/150':
            img = soup.select_one('img.course-image, img.course-thumbnail, img[alt*="course"], img[alt*="Course"]')
            if img and img.get('src'):
                thumbnail = img['src']

        # استخراج السعر الأصلي للدورة
        original_price = "49.99$"  # سعر افتراضي

        # البحث عن السعر في صفحة الدورة
        price_elements = soup.select('.text-2xl.font-medium.text-white, .text-xl.font-medium.text-white, .text-lg.font-medium.text-white, div[class*="price"], span[class*="price"]')
        for element in price_elements:
            text = element.get_text().strip()
            # البحث عن نمط السعر (مثل $13.99 أو 13.99$)
            price_match = re.search(r'(\$\d+\.\d+|\d+\.\d+\$|\$\d+|\d+\$)', text)
            if price_match:
                original_price = price_match.group(1)
                break

        # إذا لم نجد السعر في العناصر المحددة، نبحث في كل النص
        if original_price == "49.99$":
            for element in soup.select('div, span, p, h1, h2, h3, h4, h5, h6'):
                text = element.get_text().strip()
                price_match = re.search(r'(\$\d+\.\d+|\d+\.\d+\$|\$\d+|\d+\$)', text)
                if price_match:
                    original_price = price_match.group(1)
                    # التأكد من أن السعر منطقي (أكبر من 5 دولارات)
                    price_value = float(re.sub(r'[^\d.]', '', original_price))
                    if price_value > 5:
                        break

        return udemy_link['href'] if udemy_link else None, coupons_left, thumbnail, original_price

    except Exception as e:
        logger.error(f"خطأ في استخراج رابط Udemy: {e}")
        return None, 0, 'https://via.placeholder.com/150', "49.99$"

async def send_course_message(update: Update, course: dict) -> None:
    """إرسال رسالة الدورة بالتنسيق المطلوب"""
    try:
        # تحضير نص الرسالة بالتنسيق الاحترافي
        language_flag = "🇸🇦" if course.get('language') == 'ar' else "🇺🇸"

        title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
        random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
        random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

        # تحديد سعر الدورة الأصلي من البيانات المستخرجة
        original_price = course.get('original_price', "49.99$")

        # تحديد تاريخ النشر (التاريخ الحالي)
        current_date = datetime.now().strftime("%Y-%m-%d")

        # التحقق من حالة VIP للمستخدم
        user_id = update.effective_user.id
        is_vip_user = vip_manager.is_vip(user_id)

        # استخدام الرابط الأصلي مباشرة (تم إلغاء نظام الاختصار)
        course_link = course['link']

        message = (
            f"{random_emoji1} كورس جديد متاح مجاناً! {random_emoji2}\n\n"
            f"📝 {course['title']}\n\n"
            f"💰 السعر الأصلي: {original_price}\n"
            f"🎁 السعر الآن: مجاني (خصم 100%)\n"
            f"🔑 كوبون: مفعل تلقائي\n"
            f"🏢 المنصة: Udemy\n"
            f"📅 تاريخ النشر: {current_date}\n"
            f"👥 الكوبونات المتبقية: {course.get('coupons_left', '100')}\n"
            f"🌐 اللغة: {language_flag}\n\n"
        )

        if not is_vip_user:
            vip_info = """
💎 <b>ميزة VIP</b> 💎
احصل على روابط مباشرة للدورات بدون اختصار وبدون إعلانات!
"""
            message += vip_info
        else:
            # للمستخدمين VIP
            vip_info = """
💎 <b>أنت عضو VIP!</b> 💎
يمكنك الحصول على روابط مباشرة للدورات بدون إعلانات.
"""
            message += vip_info

        message += f"\n⚡ سارع بالتسجيل قبل انتهاء العرض! ⚡"

        # إنشاء أزرار الدورة
        keyboard = [
            [InlineKeyboardButton("🔥 الحصول على الكوبون 🔥", url=course_link)]
        ]

        # تم حذف زر معاينة الدورة - البوت أصبح مدفوع

        keyboard.append([InlineKeyboardButton("📢 قناة الدورات", url='https://t.me/udmePro')])

        # إنشاء معرف فريد للدورة باستخدام hash للعنوان
        course_id = str(abs(hash(course['title'])))

        if is_admin(user_id):
            keyboard.append([InlineKeyboardButton("📢 إرسال إلى القناة", callback_data=f'send_channel_{course_id}')])

        if CHANNEL_USERNAME:
            keyboard.append([InlineKeyboardButton("🔔 اشترك في القناة", url=f"https://t.me/{CHANNEL_USERNAME}")])

        if not is_vip_user:
            keyboard.append([InlineKeyboardButton("💎 اشترك في خدمة VIP", callback_data="vip_subscribe")])

        reply_markup = InlineKeyboardMarkup(keyboard)

        # إرسال الرسالة مع الصورة إذا كانت متوفرة
        if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
            # إرسال الرسالة مع الصورة
            await update.message.reply_photo(
                photo=course['thumbnail'],
                caption=message,
                reply_markup=reply_markup,
                parse_mode='HTML'  # استخدام HTML للتنسيق
            )
        else:
            # إرسال الرسالة بدون صورة
            await update.message.reply_text(
                text=message,
                reply_markup=reply_markup,
                disable_web_page_preview=True,
                parse_mode='HTML'  # استخدام HTML للتنسيق
            )

    except Exception as e:
        logger.error(f"خطأ في إرسال رسالة الدورة: {e}")
        # محاولة إرسال الرسالة بدون صورة في حالة حدوث خطأ
        try:
            # تبسيط الرسالة في حالة الخطأ
            simple_message = (
                f"🎓 {course['title']}\n\n"
                f"👥 الكوبونات المتبقية: {course.get('coupons_left', '100')}\n"
                f"🌐 اللغة: {language_flag}\n"
            )

            await update.message.reply_text(
                text=simple_message,
                reply_markup=reply_markup,
                disable_web_page_preview=True
            )
        except Exception as e2:
            logger.error(f"خطأ في إرسال الرسالة البديلة: {e2}")

async def process_page(page, language='ar'):
    """معالجة صفحة واحدة من الدورات"""
    courses = []
    try:
        url = f'{Config.BASE_URL}/coupons?page={page}'

        # استخدام التخزين المؤقت للطلبات
        html_content = cached_request(url)
        soup = BeautifulSoup(html_content, 'html.parser')

        flag_icon = 'ar.svg' if language == 'ar' else 'us.svg'

        # استخدام محدد CSS أكثر دقة
        course_divs = soup.select('div.relative')

        # تسجيل عدد الدورات التي تم العثور عليها
        logger.info(f"تم العثور على {len(course_divs)} دورة في الصفحة {page}")

        for course_div in course_divs:
            # التحقق من لغة الدورة
            if course_div.find('img', src=lambda x: x and flag_icon in x):
                title_elem = course_div.find('h2')
                link_elem = course_div.find('a', href=lambda x: x and '/udemy/' in x)

                if title_elem and link_elem:
                    title = bleach.clean(title_elem.get_text(strip=True))
                    full_link = f"{Config.BASE_URL}{link_elem['href']}"

                    try:
                        # التحقق من حالة الكوبون
                        coupon_status = check_course_coupon(full_link)

                        # فقط متابعة المعالجة إذا كان الكوبون مجاني بالكامل
                        if coupon_status == "paid_with_coupon":
                            # استخراج رابط Udemy وعدد الكوبونات المتبقية والصورة والسعر الأصلي في طلب واحد
                            udemy_link, coupons_left, thumbnail, original_price = extract_udemy_link_and_coupons_left(full_link)

                            # فقط إضافة الدورات التي لديها كوبونات متبقية ورابط Udemy صالح
                            if udemy_link and coupons_left > 0:
                                # التحقق من كون الدورة محظورة
                                if is_course_blocked(udemy_link, title):
                                    logger.info(f"تم تجاهل دورة محظورة: {title}")
                                    continue

                                courses.append({
                                    'title': title,
                                    'link': udemy_link,
                                    'comidoc_link': full_link,
                                    'thumbnail': thumbnail,
                                    'coupons_left': coupons_left,
                                    'original_price': original_price,
                                    'language': language,
                                    'timestamp': datetime.now(timezone.utc).isoformat(),
                                    'status': coupon_status
                                })
                                logger.info(f"تمت إضافة دورة مجانية: {title}")
                        elif coupon_status == "discounted":
                            logger.debug(f"تم تخطي دورة بخصم جزئي: {title}")
                        elif coupon_status == "expired":
                            logger.debug(f"تم تخطي دورة منتهية الصلاحية: {title}")

                    except Exception as e:
                        logger.error(f"خطأ في جلب تفاصيل الدورة {full_link}: {e}")
                        continue

        return courses

    except Exception as e:
        logger.error(f"خطأ في معالجة الصفحة {page}: {e}")
        return []



async def fetch_all_courses(language='ar'):
    """جلب جميع الدورات باستخدام المعالجة المتوازية"""
    # استخدام عدد الصفحات المحدد في الإعدادات
    total_pages = Config.MAX_PAGES
    all_courses = []

    # تسجيل بداية عملية جلب الدورات
    start_time = time.time()
    logger.info(f"بدء جلب الدورات من {total_pages} صفحة...")

    # استخدام asyncio.gather مع تحديد عدد المهام المتزامنة
    # تقسيم المهام إلى مجموعات لتجنب استهلاك الذاكرة
    batch_size = Config.CONCURRENT_REQUESTS
    all_results = []

    for i in range(0, total_pages, batch_size):
        # تحديد نطاق الصفحات للمجموعة الحالية
        batch_pages = range(i + 1, min(i + batch_size + 1, total_pages + 1))
        logger.info(f"معالجة الصفحات من {batch_pages.start} إلى {batch_pages.stop - 1}...")

        # إنشاء مهام للمجموعة الحالية
        tasks = [process_page(page, language) for page in batch_pages]

        # تنفيذ المهام بالتوازي
        batch_results = await asyncio.gather(*tasks)
        all_results.extend(batch_results)

        # تسجيل تقدم العملية
        logger.info(f"تم الانتهاء من معالجة {len(batch_results)} صفحة")

    # تجميع النتائج
    for courses in all_results:
        all_courses.extend(courses)

    # حفظ الدورات في الملف
    try:
        with open(Config.COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(all_courses, f, ensure_ascii=False, indent=2)

        # حساب الوقت المستغرق
        elapsed_time = time.time() - start_time
        logger.info(f"تم حفظ {len(all_courses)} دورة بنجاح في {elapsed_time:.2f} ثانية")
    except Exception as e:
        logger.error(f"خطأ في حفظ الدورات: {e}")

    return all_courses

async def start(update: Update, _context: ContextTypes.DEFAULT_TYPE) -> None:
    """رسالة البداية"""
    user_id = update.effective_user.id

    # التحقق من صلاحية المستخدم
    if not await check_user_authorization(update):
        return

    # التحقق من حالة VIP للمستخدم
    is_vip_user = vip_manager.is_vip(user_id)
    is_user_admin = is_admin(user_id)

    # إنشاء أزرار متجاورة بدلاً من أزرار متراصة
    # الصف الأول: الدورات العربية والإنجليزية
    row1 = [
        InlineKeyboardButton("🇸🇦 الدورات العربية", callback_data='ar_courses'),
        InlineKeyboardButton("🇺🇸 الدورات الإنجليزية", callback_data='en_courses')
    ]

    # الصف الثاني: دورات ديسكوديمي
    row2 = [
        InlineKeyboardButton("💰 دورات ديسكوديمي المدفوعة", callback_data='discudemy_courses')
    ]

    # الصف الثالث: دورات Real Discount
    row2_5 = [
        InlineKeyboardButton("🔥 دورات Real Discount المدفوعة", callback_data='real_discount_courses')
    ]

    # الصف الثالث: قناة الدورات وزر VIP
    row3 = [
        InlineKeyboardButton("📢 قناة الدورات", url='https://t.me/udmePro')
    ]

    if is_vip_user:
        row3.append(InlineKeyboardButton("💎 معلومات VIP", callback_data='vip_info'))
    else:
        row3.append(InlineKeyboardButton("💎 اشترك في VIP", callback_data='vip_subscribe'))

    # إنشاء لوحة المفاتيح مع الصفوف الأساسية
    keyboard = [row1, row2, row2_5, row3]

    # إضافة أزرار تحديث لأعضاء VIP (وليس المشرفين)
    if is_vip_user and not is_user_admin:
        # أزرار التحديث لأعضاء VIP
        vip_row1 = [
            InlineKeyboardButton("🇸🇦 تحديث عربية", callback_data='update_comidoc'),
            InlineKeyboardButton("🇺🇸 تحديث إنجليزية", callback_data='update_en_courses')
        ]

        vip_row2 = [
            InlineKeyboardButton("💰 تحديث Discudemy", callback_data='update_discudemy'),
            InlineKeyboardButton("🔥 تحديث Real Discount", callback_data='update_real_discount')
        ]

        keyboard.extend([vip_row1, vip_row2])

    # إضافة أزرار التحديث للمشرفين فقط
    if is_user_admin:
        # أزرار التحديث المنفصلة
        admin_row1 = [
            InlineKeyboardButton("🇸🇦 تحديث عربية", callback_data='update_comidoc'),
            InlineKeyboardButton("🇺🇸 تحديث إنجليزية", callback_data='update_en_courses')
        ]

        # أزرار المواقع الأخرى
        admin_row2 = [
            InlineKeyboardButton("💰 تحديث Discudemy", callback_data='update_discudemy'),
            InlineKeyboardButton("🔥 تحديث Real Discount", callback_data='update_real_discount')
        ]

        # تحديث جميع الدورات
        admin_row3 = [
            InlineKeyboardButton("🔄 تحديث جميع الدورات", callback_data='update_all_courses')
        ]

        # إضافة أزرار المشرف للوحة المفاتيح
        keyboard.extend([admin_row1, admin_row2, admin_row3])

    # إضافة صف رابع لأزرار المشرف إذا كان المستخدم مشرفًا
    if is_user_admin:
        keyboard.append([InlineKeyboardButton("⚙️ لوحة المشرف", callback_data='admin_panel')])

    reply_markup = InlineKeyboardMarkup(keyboard)

    # قائمة من رسائل الترحيب المتنوعة
    welcome_templates = [
        "مرحباً بك في عالم الكورسات المجانية!",
        "أهلاً بك في بوت كورسات Udemy المجانية!",
        "مرحباً بك في رحلة التعلم المجاني!",
        "أهلاً وسهلاً بك في بوت الكورسات المجانية!",
        "مرحباً بك في بوت الفرص التعليمية المجانية!",
        "أهلاً بك في مركز الكورسات المجانية!",
        "مرحباً بك في بوابة التعلم المجاني!",
        "أهلاً وسهلاً في بوت كورسات Udemy المجانية!",
        "مرحباً بك في عالم المعرفة المجانية!",
        "أهلاً بك في منصة الكورسات المجانية!",
        "أهلاً بك في بوت الكورسات التعليمية المجانية!",
        "مرحباً بك في مركز التعلم الإلكتروني المجاني!",
        "أهلاً وسهلاً بك في عالم الدورات المجانية!",
        "مرحباً بك في بوابة المعرفة المجانية!",
        "أهلاً بك في مجتمع التعلم المجاني!",
        "مرحباً بك في منصة الفرص التعليمية المجانية!",
        "أهلاً بك في رحلة تطوير مهاراتك مجاناً!",
        "مرحباً بك في بوت الكوبونات المجانية لدورات Udemy!",
        "أهلاً بك في مركز الكوبونات المجانية!",
        "مرحباً بك في عالم التعلم الذاتي المجاني!"
    ]

    # قائمة من الرموز التعبيرية المتنوعة للترحيب
    welcome_emojis = ["🌟", "✨", "🚀", "📚", "🔥", "💫", "🎓", "⭐", "🎓", "💡", "🎯", "💻", "📱", "🖥️", "📊", "🎨", "🎬", "🎮", "🎵", "📝", "🏆", "🌠", "🎁", "💎", "🔔", "🎊", "🎉", "🌺", "🌸", "🌼", "⏱", "🌹", "🍀", "🌞", "⚡", "🎖", "🌊", "🌄", "📉", "🖼"]

    # قائمة من الرموز التعبيرية للتعليم والتكنولوجيا
    education_emojis = ["📚", "🎓", "📝", "✏️", "📖", "🔍", "💡", "🧠", "📊", "📈", "🖥️", "💻", "📱", "⌨️", "🖱️", "🎯", "🏆", "🥇", "📋", "📌"]

    # قائمة من الرموز التعبيرية للتحفيز
    motivation_emojis = ["🚀", "⚡", "💪", "🔥", "✨", "🌟", "💯", "🏅", "🎖️", "🏆", "🥇", "⭐", "☀️", "⭐", "💫", "🌠", "🎯", "🧿", "💎", "🌺"]

    import random

    # اختيار رسالة ترحيب عشوائية
    welcome_template = random.choice(welcome_templates)

    # اختيار رموز تعبيرية عشوائية من كل فئة
    welcome_emoji = random.choice(welcome_emojis)
    education_emoji = random.choice(education_emojis)
    motivation_emoji = random.choice(motivation_emojis)

    # اختيار رموز إضافية للتزيين
    decoration_emoji1 = random.choice(welcome_emojis)
    decoration_emoji2 = random.choice(welcome_emojis)

    # إنشاء نمط زخرفي عشوائي للترحيب
    decoration_patterns = [
        f"{decoration_emoji1} {decoration_emoji2} {decoration_emoji1}",
        f"{decoration_emoji1}✧{decoration_emoji2}✧{decoration_emoji1}",
        f"・{decoration_emoji1}・{decoration_emoji2}・{decoration_emoji1}・",
        f"━━━ {decoration_emoji1} ━━━",
        f"⋆｡°✩ {decoration_emoji1} ✩°｡⋆",
        f"★彡 {decoration_emoji1} 彡★",
        f"⊱ {decoration_emoji1} {decoration_emoji2} {decoration_emoji1} ⊰",
        f"❅ {decoration_emoji1} ❅ {decoration_emoji2} ❅",
        f"✿ {decoration_emoji1} ✿",
        f"⋆⭒⋆ {decoration_emoji1} ⋆⭒⋆"
    ]

    decoration = random.choice(decoration_patterns)

    # إنشاء رسالة الترحيب مع الرموز العشوائية
    welcome_formats = [
        f"{decoration}\n\n{welcome_emoji} <b>{welcome_template}</b> {motivation_emoji}\n\n",
        f"{welcome_emoji} <b>{welcome_template}</b> {motivation_emoji}\n{decoration}\n\n",
        f"{decoration}\n{welcome_emoji} <b>{welcome_template}</b> {motivation_emoji}\n{decoration}\n\n",
        f"{welcome_emoji} {motivation_emoji} {welcome_emoji}\n<b>{welcome_template}</b>\n{decoration}\n\n",
        f"{decoration}\n\n{welcome_emoji} <b>{welcome_template}</b>\n\n"
    ]

    welcome_message = random.choice(welcome_formats)

    # إضافة معلومات VIP
    welcome_message += f"{education_emoji} /vip_info - عرض معلومات VIP الخاصة بك\n"

    # إضافة معلومات للمستخدم بشكل ديناميكي
    if is_vip_user:
        # رسالة VIP
        welcome_message += f"\n💎 <b>أنت عضو VIP مميز!</b> 💎\n"
        welcome_message += f"✅ وصول كامل لجميع الدورات\n"
        welcome_message += f"✅ تحديثات فورية\n"
        welcome_message += f"✅ دعم فني مخصص\n"
    elif is_user_admin:
        # رسالة المشرف
        welcome_message += f"\n🔑 <b>أهلاً بك أيها المشرف!</b> 🔑\n"
        welcome_message += f"✅ وصول كامل لجميع الميزات\n"
        welcome_message += f"✅ لوحة تحكم متقدمة\n"
        welcome_message += f"✅ إدارة المستخدمين والدورات\n"

    # إضافة أوامر المشرف إذا كان المستخدم مشرفًا
    if is_user_admin:
        # رموز المشرف المتنوعة
        admin_emojis = ["🔑", "⚙️", "🛠️", "🔧", "👨‍💻", "👩‍💻", "🔐", "🔒", "🔓", "📊"]

        # اختيار رموز عشوائية
        admin_emoji1 = random.choice(admin_emojis)
        admin_emoji2 = random.choice(admin_emojis)
        admin_emoji3 = random.choice(admin_emojis)

        # إنشاء قسم أوامر المشرف بتنسيق جذاب
        admin_commands = f"""
{admin_emoji1} <b>أوامر المشرف:</b> {admin_emoji2}
{admin_emoji3} /add_vip - إضافة عضو VIP (المعرف، المدة، الاسم)
{admin_emoji3} /send_to_channel - إرسال الدورات للقناة
{admin_emoji3} /clean_courses - تنظيف الكوبونات المنتهية
"""
        welcome_message += f"\n{admin_commands}\n"

    # قائمة من رسائل الختام المتنوعة
    closing_messages = [
        "اختر من القائمة أدناه:",
        "يمكنك اختيار أحد الخيارات أدناه:",
        "استكشف الخيارات المتاحة أدناه:",
        "اختر ما يناسبك من القائمة التالية:",
        "تصفح الخيارات المتاحة من خلال الأزرار أدناه:"
    ]

    # اختيار رسالة ختامية عشوائية
    closing_message = random.choice(closing_messages)
    welcome_message += f"\n{closing_message}"

    # صورة الترحيب
    welcome_image = "https://g.top4top.io/p_33930bt4w1.jpg"

    try:
        # إرسال الرسالة مع الصورة
        await update.message.reply_photo(
            photo=welcome_image,
            caption=welcome_message,
            reply_markup=reply_markup,
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"خطأ في إرسال صورة الترحيب: {e}")
        # في حالة فشل إرسال الصورة، نرسل رسالة نصية فقط
        await update.message.reply_text(
            welcome_message,
            reply_markup=reply_markup,
            parse_mode='HTML'
        )

async def show_courses(update: Update, _context: ContextTypes.DEFAULT_TYPE, filters=None):
    """عرض الدورات مع التصفية"""
    query = update.callback_query
    user_id = update.effective_user.id

    # التحقق من صلاحية المستخدم
    if not is_authorized_user(user_id):
        # إنشاء أزرار للمستخدمين غير المخولين
        keyboard = [
            [InlineKeyboardButton("📺 القناة المجانية", url="https://t.me/udmePro")],
            [InlineKeyboardButton("💎 اشترك في VIP", url="https://t.me/GurusVIP")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.message.reply_text(
            "🔒 **هذه الميزة متاحة للأعضاء المميزين فقط**\n\n"
            "📺 **للحصول على الدورات العربية مجاناً:**\n"
            "يمكنك متابعة قناتنا المجانية حيث ننشر الدورات بروابط مختصرة\n"
            "(الروابط المختصرة تساعدنا في تغطية تكاليف الاستضافة)\n\n"
            "💎 **للحصول على روابط مباشرة:**\n"
            "اشترك في خدمة VIP للحصول على روابط مباشرة بدون اختصار\n\n"
            "📞 **للاشتراك:** @GurusVIP",
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
        return

    courses = []

    # التحقق من تضارب العمليات
    if await check_user_operation_conflict(update, 'show_courses'):
        return

    # إرسال رسالة انتظار
    waiting_message = await send_waiting_message(update, 'show_courses', user_id)

    # بدء العملية
    if not user_operation_manager.start_operation(user_id, 'show_courses', waiting_message.message_id if waiting_message else None):
        await query.message.reply_text("❌ فشل في بدء العملية، حاول مرة أخرى")
        return

    try:
        # تحديث رسالة الانتظار
        await update_waiting_message(waiting_message, "📖 جاري قراءة قاعدة البيانات...", user_id)

        with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
            all_courses = json.load(f)

        # تحديث رسالة الانتظار
        await update_waiting_message(waiting_message, "🔍 جاري تطبيق ...", user_id)

        if filters:
            courses = [c for c in all_courses if all(c.get(k) == v for k, v in filters.items())]
        else:
            courses = all_courses

        # فلترة الدورات المحظورة
        courses = [c for c in courses if not is_course_blocked(c.get('link', ''), c.get('title', ''))]

        # عكس ترتيب الدورات من الأسفل إلى الأعلى
        courses = list(reversed(courses))

        if not courses:
            await update_waiting_message(waiting_message, "⚠️ لا توجد دورات متوفرة حالياً", user_id)
            return



        # التحقق من حالة VIP للمستخدم
        user_id = update.effective_user.id
        is_vip_user = vip_manager.is_vip(user_id)

        for course in courses:
            # إضافة إيموجي عشوائي للعنوان
            title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
            random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
            random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

            # تحديد سعر الدورة الأصلي من البيانات المستخرجة
            original_price = course.get('original_price', "49.99$")

            # تحديد العلم حسب اللغة
            language_flag = "🇸🇦" if course.get('language') == 'ar' else "🇺🇸"

            # تحديد تاريخ النشر (التاريخ الحالي)
            current_date = datetime.now().strftime("%Y-%m-%d")

            # استخدام الرابط الأصلي مباشرة (تم إلغاء نظام الاختصار)
            course_link = course['link']

            message = (
                f"{random_emoji1} كورس جديد متاح مجاناً! {random_emoji2}\n\n"
                f"📝 *{course['title']}*\n\n"
                f"💰 السعر الأصلي: {original_price}\n"
                f"🎁 السعر الآن: مجاني (خصم 100%)\n"
                f"🔑 كوبون: مفعل تلقائي\n"
                f"🏢 المنصة: Udemy\n"
                f"📅 تاريخ النشر: {current_date}\n"
                f"👥 الكوبونات المتبقية: {course.get('coupons_left', '100')}\n"
                f"🌐 اللغة: {language_flag}\n\n"
            )

            # إضافة معلومات VIP للمستخدمين غير VIP فقط
            if not is_vip_user:
                vip_info = """
💎 *ميزة VIP* 💎
انضم لعضوية VIP للوصول الكامل لجميع الدورات!
"""
                message += vip_info
            else:
                # للمستخدمين VIP
                vip_info = """
💎 *أنت عضو VIP!* 💎
استمتع بالوصول الكامل لجميع الدورات.
"""
                message += vip_info

            message += f"\n⚡ سارع بالتسجيل قبل انتهاء العرض! ⚡"

            # إنشاء معرف فريد للدورة باستخدام hash للعنوان
            # استخدام رقم موجب دائمًا للمعرف
            course_id = str(abs(hash(course['title'])))
            logger.debug(f"إنشاء معرف للدورة: {course['title']} -> {course_id}")

            # إنشاء أزرار متجاورة بدلاً من أزرار متراصة
            # الصف الأول: زر الحصول على الكوبون (يبقى وحده لأهميته)
            row1 = [
                InlineKeyboardButton("🔥 الحصول على الكوبون 🔥", url=course_link)
            ]

            # تم حذف زر معاينة الدورة - البوت أصبح مدفوع

            # الصف الثاني: زر القناة وزر VIP
            row2 = []

            if CHANNEL_USERNAME:
                row2.append(InlineKeyboardButton("🔔 اشترك في القناة", url=f"https://t.me/{CHANNEL_USERNAME}"))

            # إضافة زر للاشتراك في خدمة VIP للمستخدمين غير VIP
            if not is_vip_user:
                row2.append(InlineKeyboardButton("💎 اشترك في VIP", callback_data="vip_subscribe"))

            # إنشاء لوحة المفاتيح مع الصفوف
            keyboard = [row1]

            # إضافة الصف الثاني إذا كان يحتوي على أزرار
            if row2:
                keyboard.append(row2)

            # إضافة زر "إرسال إلى القناة" للمشرفين فقط
            if is_admin(user_id):
                keyboard.append([InlineKeyboardButton("📢 إرسال إلى القناة", callback_data=f'send_channel_{course_id}')])

            reply_markup = InlineKeyboardMarkup(keyboard)

            # إرسال الرسالة مع الصورة إذا كانت متوفرة
            if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
                try:
                    # إرسال الرسالة مع الصورة
                    await query.message.reply_photo(
                        photo=course['thumbnail'],
                        caption=message,
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )
                except Exception as e:
                    logger.error(f"خطأ في إرسال الصورة: {e}")
                    # إرسال بدون صورة في حالة الخطأ
                    await query.message.reply_text(
                        message,
                        parse_mode='Markdown',
                        reply_markup=reply_markup,
                        disable_web_page_preview=True
                    )
            else:
                # إرسال الرسالة بدون صورة
                await query.message.reply_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup,
                    disable_web_page_preview=True
                )

    except FileNotFoundError:
        await update_waiting_message(waiting_message, "❌ لم يتم العثور على قاعدة البيانات", user_id)
        logger.error("ملف الدورات غير موجود")
    except Exception as e:
        await update_waiting_message(waiting_message, "❌ حدث خطأ أثناء جلب الدورات", user_id)
        logger.error(f"خطأ في عرض الدورات: {e}")
    finally:
        # إنهاء العملية
        user_operation_manager.end_operation(user_id)

async def clear_cache(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تنظيف الذاكرة المؤقتة"""
    if not await admin_only(update, context):
        return

    message = await update.callback_query.message.reply_text("جاري تنظيف الذاكرة المؤقتة...")

    try:
        # تنظيف ذاكرة التخزين المؤقت
        cache.clear()
        cached_request.cache_clear()

        # تسجيل نجاح العملية
        logger.info("تم تنظيف الذاكرة المؤقتة بنجاح")
        await message.edit_text("✅ تم تنظيف الذاكرة المؤقتة بنجاح")

    except Exception as e:
        logger.error(f"خطأ في تنظيف الذاكرة المؤقتة: {e}")
        await message.edit_text("❌ حدث خطأ في تنظيف الذاكرة المؤقتة")

async def verify_coupons_manually(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """التحقق اليدوي من صلاحية الكوبونات"""
    if not await admin_only(update, context):
        return

    message = await update.callback_query.message.reply_text("جاري التحقق من صلاحية الكوبونات...")

    try:
        # استدعاء وظيفة التحقق من الكوبونات
        verify_coupons_job()

        # قراءة الدورات المحدثة
        with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
            courses = json.load(f)

        # عرض نتائج التحقق
        await message.edit_text(
            f"✅ تم التحقق من صلاحية الكوبونات بنجاح!\n"
            f"عدد الدورات النشطة: {len(courses)}\n"
            f"آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )

    except Exception as e:
        logger.error(f"خطأ في التحقق اليدوي من صلاحية الكوبونات: {e}")
        await message.edit_text("❌ حدث خطأ في التحقق من صلاحية الكوبونات")

async def send_to_channel_direct(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """إرسال الدورة مباشرة إلى القناة"""
    query = update.callback_query

    try:
        # إرسال رسالة للمستخدم
        status_message = await query.message.reply_text("جاري إرسال الدورة إلى القناة...")

        # استخراج معرف الدورة من البيانات
        # التنسيق: send_to_channel_COURSE_ID
        if query.data.startswith('send_to_channel_'):
            course_id = query.data.replace('send_to_channel_', '')
        else:
            parts = query.data.split('_')
            course_id = parts[2] if len(parts) >= 3 else parts[1]

        logger.info(f"معرف الدورة المطلوب إرسالها: {course_id}")
        logger.info(f"بيانات الاستعلام الأصلية: {query.data}")

        # التحقق من صحة معرف الدورة
        if not course_id or course_id == "channel":
            await status_message.edit_text("❌ معرف الدورة غير صالح")
            return

        # قراءة الدورات من ملف comidoc
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                comidoc_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            comidoc_courses = []

        # قراءة الدورات من ملف discudemy
        try:
            with open(Config.DISCUDEMY_COURSES_FILE, 'r', encoding='utf-8') as f:
                discudemy_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            discudemy_courses = []

        # قراءة الدورات من ملف real.discount
        try:
            with open(Config.REAL_DISCOUNT_COURSES_FILE, 'r', encoding='utf-8') as f:
                real_discount_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            real_discount_courses = []

        # دمج جميع الدورات
        all_courses = comidoc_courses + discudemy_courses + real_discount_courses
        logger.info(f"تم تحميل {len(comidoc_courses)} دورة من comidoc و {len(discudemy_courses)} دورة من discudemy و {len(real_discount_courses)} دورة من real.discount للبحث")

        # البحث عن الدورة بالمعرف
        course = None
        logger.info(f"بدء البحث عن الدورة بالمعرف: {course_id}")

        for i, c in enumerate(all_courses):
            # استخدام abs(hash()) لضمان الحصول على قيمة موجبة متطابقة مع المعرف المستخدم في الزر
            current_hash = str(abs(hash(c['title'])))

            # طباعة تفاصيل البحث لأول 5 دورات
            if i < 5:
                logger.info(f"مقارنة {current_hash} مع {course_id} للدورة: {c['title'][:50]}...")

            if current_hash == course_id:
                course = c
                # تحديد مصدر الدورة
                if c in real_discount_courses:
                    source = 'real.discount'
                elif c in discudemy_courses:
                    source = 'discudemy'
                else:
                    source = 'comidoc'
                logger.info(f"✅ تم العثور على الدورة في {source}: {c['title']}")
                break

        if not course:
            logger.error(f"❌ لم يتم العثور على الدورة بالمعرف: {course_id}")
            logger.error(f"إجمالي الدورات المتاحة: {len(all_courses)}")
            logger.error(f"عينة من معرفات الدورات: {[str(abs(hash(c['title']))) for c in all_courses[:3]]}")
            await status_message.edit_text("❌ لم يتم العثور على الدورة")
            return

        logger.info(f"تم العثور على الدورة: {course['title']}")

        # قراءة الدورات المرسلة سابقًا
        try:
            with open(Config.SENT_COURSES_FILE, 'r', encoding='utf-8') as f:
                sent_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            sent_courses = []

        # التحقق مما إذا كانت الدورة قد تم إرسالها بالفعل
        if any(c.get('link') == course['link'] for c in sent_courses):
            logger.info(f"الدورة تم إرسالها بالفعل: {course['title']}")
            await status_message.edit_text("⚠️ تم إرسال هذه الدورة بالفعل إلى القناة")
            return

        # إعداد رسالة الإرسال
        await status_message.edit_text("⏳ جاري إرسال الدورة إلى القناة...")

        # تحضير نص الرسالة بالتنسيق الاحترافي
        language_flag = "🇸🇦" if course.get('language') == 'ar' else "🇺🇸"

        # إضافة إيموجي عشوائي للعنوان
        title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
        random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
        random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

        # تحديد سعر الدورة الأصلي من البيانات المستخرجة
        original_price = course.get('original_price', "49.99$")

        # تحديد تاريخ النشر (التاريخ الحالي)
        current_date = datetime.now().strftime("%Y-%m-%d")

        # استخدام الرابط الأصلي مباشرة (تم إلغاء نظام الاختصار)
        course_link = course['link']

        message_text = (
            f"{random_emoji1} كورس جديد متاح مجاناً! {random_emoji2}\n\n"
            f"📝 {course['title']}\n\n"
            f"💰 السعر الأصلي: {original_price}\n"
            f"🎁 السعر الآن: مجاني (خصم 100%)\n"
            f"🔑 كوبون: مفعل تلقائي\n"
            f"🏢 المنصة: Udemy\n"
            f"📅 تاريخ النشر: {current_date}\n"
            f"👥 الكوبونات المتبقية: {course.get('coupons_left', '100')}\n"
            f"🌐 اللغة: {language_flag}\n\n"
            f"💎 للحصول على روابط مباشرة بدون اختصار، اشترك في خدمة VIP!\n\n"
            f"⚡ سارع بالتسجيل قبل انتهاء العرض! ⚡"
        )

        # إنشاء أزرار متجاورة
        # الصف الأول: زر الحصول على الكوبون (يبقى وحده لأهميته)
        row1 = [
            InlineKeyboardButton("🔥 الحصول على الكوبون 🔥", url=course_link)
        ]

        # الصف الثاني: زر القناة وزر VIP
        row2 = []

        if CHANNEL_USERNAME:
            row2.append(InlineKeyboardButton("🔔 اشترك في القناة", url=f"https://t.me/{CHANNEL_USERNAME}"))

        # إضافة زر للاشتراك في خدمة VIP
        row2.append(InlineKeyboardButton("💎 اشترك في VIP", url="https://t.me/GurusVIP"))

        # إنشاء لوحة المفاتيح مع الصفوف
        keyboard = [row1, row2]

        reply_markup = InlineKeyboardMarkup(keyboard)

        # طباعة معلومات التصحيح
        logger.info(f"محاولة إرسال دورة إلى القناة: {course['title']}")

        # التحقق من صلاحيات البوت في القناة
        try:
            # استخدام معرف القناة الصحيح
            channel_id = -1002613463650  # معرف القناة الصحيح

            # محاولة إرسال رسالة اختبار للتحقق من الصلاحيات
            try:
                chat_info = await context.bot.get_chat(channel_id)
                logger.info(f"معلومات القناة: {chat_info.title} (ID: {chat_info.id})")
            except Exception as e:
                logger.warning(f"لم يتمكن من الحصول على معلومات القناة: {e}")

            # إرسال الرسالة إلى القناة مع الصورة إذا كانت متوفرة
            if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
                # إرسال الرسالة مع الصورة
                await context.bot.send_photo(
                    chat_id=channel_id,  # معرف القناة
                    photo=course['thumbnail'],
                    caption=message_text,
                    reply_markup=reply_markup,
                    parse_mode=None  # عدم استخدام أي تنسيق لتجنب الأخطاء
                )
                logger.info("تم إرسال الرسالة مع الصورة")
            else:
                # إرسال الرسالة بدون صورة
                await context.bot.send_message(
                    chat_id=channel_id,  # معرف القناة
                    text=message_text,
                    reply_markup=reply_markup,
                    disable_web_page_preview=True,
                    parse_mode=None  # عدم استخدام أي تنسيق لتجنب الأخطاء
                )
                logger.info("تم إرسال الرسالة بدون صورة")
        except Exception as e:
            logger.error(f"خطأ في إرسال الرسالة إلى القناة: {e}")
            # محاولة إرسال الرسالة بطريقة أخرى
            try:
                channel_id = -1002613463650  # معرف القناة الصحيح
                await context.bot.send_message(
                    chat_id=channel_id,  # معرف القناة
                    text=f"كورس جديد: {course['title']}\n\nالرابط: {course_link}",
                    disable_web_page_preview=True
                )
                logger.info("تم إرسال رسالة مبسطة")
            except Exception as e2:
                logger.error(f"خطأ في إرسال الرسالة المبسطة: {e2}")
                # عرض معلومات مفصلة عن الخطأ
                error_details = f"نوع الخطأ: {type(e2).__name__}\nرسالة الخطأ: {str(e2)}"
                logger.error(f"تفاصيل الخطأ: {error_details}")
                await status_message.edit_text(f"❌ فشل إرسال الرسالة: {str(e2)}")
                return

        # إضافة الدورة إلى قائمة الدورات المرسلة
        course['sent_date'] = datetime.now(timezone.utc).isoformat()
        sent_courses.append(course)

        # حفظ الدورات المرسلة
        with open(Config.SENT_COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(sent_courses, f, ensure_ascii=False, indent=2)

        # إرسال رسالة تأكيد للمستخدم
        await status_message.edit_text(f"✅ تم إرسال الدورة بنجاح إلى القناة")
        logger.info(f"تم إرسال الدورة بنجاح: {course['title']}")

        # إضافة زر للانتقال إلى القناة (استخدم رابط القناة الصحيح إذا كان متاحًا)
        channel_keyboard = [[InlineKeyboardButton("🔔 الانتقال إلى القناة", url="https://t.me/udmePro")]]
        await query.message.reply_text(
            "✅ تم إرسال الدورة بنجاح!\nيمكنك الانتقال إلى القناة لمشاهدة الدورة.",
            reply_markup=InlineKeyboardMarkup(channel_keyboard)
        )

    except Exception as e:
        logger.error(f"خطأ في إرسال الدورة إلى القناة: {e}")
        await status_message.edit_text(f"❌ حدث خطأ في إرسال الدورة إلى القناة: {str(e)}")

        # إرسال تفاصيل الخطأ للتصحيح
        error_details = f"نوع الخطأ: {type(e).__name__}\nرسالة الخطأ: {str(e)}\nالدورة: {course['title'] if course else 'غير معروف'}"
        logger.error(f"تفاصيل الخطأ: {error_details}")

async def show_discudemy_courses(update: Update, _context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض دورات ديسكوديمي المدفوعة"""
    query = update.callback_query

    # إرسال رسالة انتظار
    message = await query.message.reply_text("جاري تحميل دورات ديسكوديمي المدفوعة...")

    try:
        # جلب دورات ديسكوديمي
        courses = await get_discudemy_courses()

        if not courses:
            await message.edit_text("❌ لم يتم العثور على دورات متاحة من ديسكوديمي")
            return

        # حذف رسالة التحميل
        await message.delete()

        # التحقق من حالة VIP للمستخدم
        user_id = update.effective_user.id
        is_vip_user = vip_manager.is_vip(user_id)

        # فلترة الدورات المحظورة
        filtered_courses = [c for c in courses if not is_course_blocked(c.get('link', ''), c.get('title', ''))]

        # عكس ترتيب الدورات لإظهار الأحدث أولاً (مثل الدورات الإنجليزية و Real Discount)
        filtered_courses_reversed = list(reversed(filtered_courses))

        # عرض الدورات (الأحدث أولاً، بحد أقصى 10 دورات)
        for course in filtered_courses_reversed[:10]:
            # إضافة إيموجي عشوائي للعنوان
            title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
            random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
            random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

            # تحديد سعر الدورة الأصلي من البيانات المستخرجة
            original_price = course.get('original_price', "Unknown")

            # تحديد العلم حسب اللغة
            language_flag = "🇸🇦" if course.get('language') == 'ar' else "🇺🇸"

            # تحديد تاريخ النشر (التاريخ الحالي)
            current_date = datetime.now().strftime("%Y-%m-%d")

            # استخدام الرابط الأصلي مباشرة (تم إلغاء نظام الاختصار)
            course_link = course['link']

            message_text = (
                f"{random_emoji1} كورس مدفوع متاح بسعر مخفض! {random_emoji2}\n\n"
                f"📝 *{course['title']}*\n\n"
                f"💰 السعر الأصلي: {original_price}\n"
                f"🎁 السعر الآن: مخفض\n"
                f"🔑 كوبون: مفعل تلقائي\n"
                f"🏢 المنصة: Udemy\n"
                f"📅 تاريخ النشر: {current_date}\n"
                f"👥 الكوبونات المتبقية: {course.get('coupons_left', '100')}\n"
                f"🌐 اللغة: {language_flag}\n\n"
            )

            # إضافة معلومات VIP للمستخدمين غير VIP فقط
            if not is_vip_user:
                vip_info = """
💎 *ميزة VIP* 💎
انضم لعضوية VIP للوصول الكامل لجميع الدورات!
"""
                message_text += vip_info
            else:
                # للمستخدمين VIP
                vip_info = """
💎 *أنت عضو VIP!* 💎
استمتع بالوصول الكامل لجميع الدورات.
"""
                message_text += vip_info

            message_text += f"\n⚡ سارع بالتسجيل قبل انتهاء العرض! ⚡"

            # إنشاء معرف فريد للدورة باستخدام hash للعنوان
            course_id = str(abs(hash(course['title'])))

            # إنشاء أزرار متجاورة
            # الصف الأول: زر الحصول على الكوبون (يبقى وحده لأهميته)
            row1 = [
                InlineKeyboardButton("🔥 الحصول على الكوبون 🔥", url=course_link)
            ]

            # تم حذف زر معاينة الدورة - البوت أصبح مدفوع

            # الصف الثاني: زر القناة وزر VIP
            row2 = []

            if CHANNEL_USERNAME:
                row2.append(InlineKeyboardButton("🔔 اشترك في القناة", url=f"https://t.me/{CHANNEL_USERNAME}"))

            # إضافة زر للاشتراك في خدمة VIP للمستخدمين غير VIP
            if not is_vip_user:
                row2.append(InlineKeyboardButton("💎 اشترك في VIP", callback_data="vip_subscribe"))

            # إنشاء لوحة المفاتيح مع الصفوف
            keyboard = [row1]

            # إضافة الصف الثاني إذا كان يحتوي على أزرار
            if row2:
                keyboard.append(row2)

            # إضافة زر "إرسال إلى القناة" للمشرفين فقط
            if is_admin(user_id):
                keyboard.append([InlineKeyboardButton("📢 إرسال إلى القناة", callback_data=f'send_channel_{course_id}')])

            reply_markup = InlineKeyboardMarkup(keyboard)

            # إرسال الرسالة مع الصورة إذا كانت متوفرة
            if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
                try:
                    # إرسال الرسالة مع الصورة
                    await query.message.reply_photo(
                        photo=course['thumbnail'],
                        caption=message_text,
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )
                except Exception as e:
                    logger.error(f"خطأ في إرسال الصورة: {e}")
                    # إرسال بدون صورة في حالة الخطأ
                    await query.message.reply_text(
                        message_text,
                        parse_mode='Markdown',
                        reply_markup=reply_markup,
                        disable_web_page_preview=True
                    )
            else:
                # إرسال الرسالة بدون صورة
                await query.message.reply_text(
                    message_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup,
                    disable_web_page_preview=True
                )

    except Exception as e:
        logger.error(f"خطأ في عرض دورات ديسكوديمي: {e}")
        await message.edit_text("❌ حدث خطأ أثناء جلب دورات ديسكوديمي")

async def show_real_discount_courses(update: Update, _context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض دورات Real Discount المدفوعة"""
    query = update.callback_query

    # إرسال رسالة تحميل
    message = await query.message.reply_text("🔄 جاري تحميل دورات Real Discount...")

    try:
        # جلب دورات Real Discount
        courses = await get_real_discount_courses()

        if not courses:
            await message.edit_text("❌ لم يتم العثور على دورات متاحة من Real Discount")
            return

        # حذف رسالة التحميل
        await message.delete()

        # التحقق من حالة VIP للمستخدم
        user_id = update.effective_user.id
        is_vip_user = vip_manager.is_vip(user_id)

        # فلترة الدورات المحظورة
        filtered_courses = [c for c in courses if not is_course_blocked(c.get('link', ''), c.get('title', ''))]

        # عكس ترتيب الدورات لإظهار الأحدث أولاً (مثل الدورات الإنجليزية)
        filtered_courses_reversed = list(reversed(filtered_courses))

        # عرض الدورات (الأحدث أولاً)
        for i, course in enumerate(filtered_courses_reversed):
            try:
                # إعداد رسالة الدورة بنفس نمط Comidoc و Discudemy

                # حساب نسبة الخصم والأسعار الحقيقية
                original_price_str = course.get('original_price', 'غير محدد')
                current_price_str = course.get('current_price', 'مجاني')

                # حساب نسبة الخصم
                discount_percentage = course.get('discount_percentage', 0)
                if not discount_percentage:
                    try:
                        original = float(str(original_price_str).replace('$', '').replace('مجاني', '0'))
                        current = float(str(current_price_str).replace('$', '').replace('مجاني', '0'))
                        if original > 0:
                            discount_percentage = int(((original - current) / original) * 100)
                    except:
                        discount_percentage = 100

                # تحديد نص السعر الحالي
                if current_price_str == 'مجاني' or course.get('price_value', 0) == 0:
                    if discount_percentage > 0 and original_price_str != 'مجاني':
                        current_price_display = f"مجاني (بدلاً من {original_price_str})"
                    else:
                        current_price_display = "مجاني"
                else:
                    current_price_display = current_price_str

                # إعداد رسالة بنفس تنسيق Comidoc/Discudemy
                course_message = f"""
📚 <b>كورس جديد متاح مجاناً!</b> 📱

📝 <b>[AR] {course.get('title', 'عنوان غير متوفر')}</b>

💰 <b>السعر الأصلي:</b> {original_price_str}
🎁 <b>السعر الآن:</b> {current_price_display}
🔑 <b>كوبون:</b> {'مفعل تلقائي' if course.get('coupon_code') else 'غير متوفر'}
🏢 <b>المنصة:</b> Udemy
📅 <b>تاريخ النشر:</b> {datetime.now().strftime('%d-%m-%Y')}
👥 <b>الكوبونات المتبقية:</b> {course.get('coupons_left', 100)}
🌐 <b>اللغة:</b> 🇸🇦
"""

                # إضافة رسالة VIP والتحفيز بنفس التنسيق
                if is_vip_user:
                    course_message += "\n\n💎 <b>أنت عضو VIP!</b> 💎\nيمكنك الحصول على روابط مباشرة للدورات بدون إعلانات."

                # إضافة رسالة التحفيز
                course_message += "\n\n⚡️ <b>سارع بالتسجيل قبل انتهاء العرض!</b> ⚡️"

                # إعداد أزرار الدورة
                keyboard = []

                # زر الحصول على الكوبون
                if course.get('link'):
                    keyboard.append([InlineKeyboardButton("⚡️ الحصول على الكوبون ⚡️", url=course['link'])])

                # زر الاشتراك في القناة
                keyboard.append([InlineKeyboardButton("📺 اشترك في القناة", url=f'https://t.me/{CHANNEL_USERNAME}')])

                # زر إرسال إلى القناة (للمشرفين فقط)
                if is_admin(user_id):
                    course_id = str(abs(hash(course['title'])))
                    keyboard.append([InlineKeyboardButton("📤 إرسال إلى القناة", callback_data=f'send_to_channel_{course_id}')])

                reply_markup = InlineKeyboardMarkup(keyboard)

                # إرسال الدورة مع الصورة
                try:
                    if course.get('thumbnail'):
                        await query.message.reply_photo(
                            photo=course['thumbnail'],
                            caption=course_message,
                            reply_markup=reply_markup,
                            parse_mode='HTML'
                        )
                    else:
                        await query.message.reply_text(
                            course_message,
                            reply_markup=reply_markup,
                            parse_mode='HTML'
                        )
                except Exception as photo_error:
                    logger.error(f"خطأ في إرسال الصورة: {photo_error}")
                    # إرسال بدون صورة في حالة الخطأ
                    await query.message.reply_text(
                        course_message,
                        reply_markup=reply_markup,
                        parse_mode='HTML'
                    )

                # توقف قصير بين الرسائل
                await asyncio.sleep(0.5)

                # إيقاف عرض الدورات لغير VIP بعد 3 دورات
                if not is_vip_user and i >= 2:  # عرض 3 دورات فقط
                    # إضافة رسالة ترقية VIP
                    vip_message = """
💎 <b>لعرض المزيد من الدورات، اشترك في VIP!</b> 💎
احصل على روابط مباشرة بدون إعلانات وعرض غير محدود للدورات!
"""
                    await query.message.reply_text(vip_message, parse_mode='HTML')
                    break

            except Exception as course_error:
                logger.error(f"خطأ في عرض دورة Real Discount: {course_error}")
                continue

    except Exception as e:
        logger.error(f"خطأ في عرض دورات Real Discount: {e}")
        await message.edit_text("❌ حدث خطأ أثناء جلب دورات Real Discount")

async def show_en_courses(update: Update, _context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض الدورات الإنجليزية المحفوظة"""
    query = update.callback_query

    # قراءة الدورات الإنجليزية من الملف
    try:
        with open(Config.EN_COURSES_FILE, 'r', encoding='utf-8') as f:
            courses = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        courses = []

    if not courses:
        await query.message.reply_text("❌ لا توجد دورات إنجليزية متاحة حالياً\n\n💡 استخدم زر تحديث الدورات الإنجليزية لجلب أحدث الدورات")
        return

    # تصفية الدورات النشطة فقط
    active_courses = [course for course in courses if course.get('coupons_left', 0) > 0]

    if not active_courses:
        await query.message.reply_text("❌ لا توجد دورات إنجليزية نشطة حالياً\n\n💡 جميع الكوبونات منتهية الصلاحية")
        return

    # عكس ترتيب الدورات لإظهار الأحدث أولاً
    active_courses_reversed = list(reversed(active_courses))

    # عرض جميع الدورات النشطة (الأحدث أولاً)
    courses_to_show = active_courses_reversed

    await query.message.reply_text(f"🇺🇸 **الدورات الإنجليزية المتاحة**\n\n📊 إجمالي الدورات النشطة: {len(active_courses)}\n🎯 عرض جميع الدورات ({len(courses_to_show)} دورة)", parse_mode='Markdown')

    for course in courses_to_show:
        try:
            # استخدام الرابط الأصلي مباشرة (تم إلغاء نظام الاختصار)
            course_link = course['link']

            # تحضير رسالة الدورة
            title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
            random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
            random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

            original_price = course.get('original_price', "49.99$")
            current_date = datetime.now().strftime("%Y-%m-%d")

            message_text = (
                f"{random_emoji1} English Course Available! {random_emoji2}\n\n"
                f"📝 {course['title']}\n\n"
                f"💰 Original Price: {original_price}\n"
                f"🎁 Current Price: Free (100% OFF)\n"
                f"🔑 Coupon: Auto Applied\n"
                f"🏢 Platform: Udemy\n"
                f"📅 Published: {current_date}\n"
                f"👥 Coupons Left: {course.get('coupons_left', '100')}\n"
                f"🌐 Language: 🇺🇸\n\n"
                f"⚡ Enroll now before the offer expires! ⚡"
            )

            # إنشاء أزرار
            keyboard = [
                [InlineKeyboardButton("🔥 Get Coupon 🔥", url=course_link)]
            ]

            # تم حذف زر معاينة الدورة - البوت أصبح مدفوع

            if CHANNEL_USERNAME:
                keyboard.append([InlineKeyboardButton("🔔 Subscribe to Channel", url=f"https://t.me/{CHANNEL_USERNAME}")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            # إرسال الدورة
            if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
                await query.message.reply_photo(
                    photo=course['thumbnail'],
                    caption=message_text,
                    reply_markup=reply_markup,
                    parse_mode=None
                )
            else:
                await query.message.reply_text(
                    text=message_text,
                    reply_markup=reply_markup,
                    disable_web_page_preview=True,
                    parse_mode=None
                )

            # انتظار قصير بين الرسائل
            await asyncio.sleep(0.5)

        except Exception as e:
            logger.error(f"خطأ في إرسال دورة إنجليزية: {e}")
            continue

async def update_en_courses(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تحديث الدورات الإنجليزية (5 صفحات فقط)"""
    user_id = update.effective_user.id

    # التحقق من التوقيت بين التحديثات (3 دقائق لأي تحديث)
    can_update, remaining_seconds = update_cooldown_manager.can_update()
    if not can_update:
        remaining_time = update_cooldown_manager.get_remaining_time_text(remaining_seconds)
        last_update_info = update_cooldown_manager.get_last_update_info()
        await update.callback_query.message.reply_text(
            f"⏰ يجب الانتظار {remaining_time} قبل أي تحديث\n\n"
            f"📊 آخر تحديث: {last_update_info}\n"
            f"🔄 هذا النظام يمنع التحديثات المتتالية التي تسبب مشاكل للمستخدمين الآخرين\n"
            f"⏱️ الحد الأدنى بين أي تحديث: 3 دقائق"
        )
        return

    # التحقق من تضارب العمليات
    if await check_user_operation_conflict(update, 'update_courses'):
        return

    # التحقق من وجود عملية تحديث أخرى
    if not _update_lock.acquire(blocking=False):
        await update.callback_query.message.reply_text("⏸️ عملية تحديث أخرى قيد التشغيل، يرجى المحاولة لاحقاً")
        return

    # إرسال رسالة انتظار
    message = await send_waiting_message(update, 'update_courses', user_id)

    # بدء العملية
    if not user_operation_manager.start_operation(user_id, 'update_en_courses', message.message_id if message else None):
        await update.callback_query.message.reply_text("❌ فشل في بدء العملية، حاول مرة أخرى")
        _update_lock.release()
        return

    try:
        # تحديث رسالة الانتظار
        await update_waiting_message(message, "🔍 جاري قراءة الدورات الإنجليزية الحالية...", user_id)

        # قراءة الدورات الحالية للمقارنة
        try:
            with open(Config.EN_COURSES_FILE, 'r', encoding='utf-8') as f:
                old_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            old_courses = []

        old_links = {course.get('link') for course in old_courses}

        # تحديث رسالة الانتظار
        await update_waiting_message(message, "🌐 جاري جلب الدورات الإنجليزية (5 صفحات)...", user_id)

        # جلب 5 صفحات فقط من الدورات الإنجليزية
        courses = []
        for page in range(1, 6):  # صفحات 1-5
            await update_waiting_message(message, f"📄 جاري فحص الصفحة {page}/5 للدورات الإنجليزية...", user_id)
            logger.info(f"جاري فحص الصفحة {page} للدورات الإنجليزية...")
            page_courses = await process_page(page, 'en')
            courses.extend(page_courses)
            logger.info(f"تم العثور على {len(page_courses)} دورة في الصفحة {page}")

        if courses:
            # تحديث رسالة الانتظار
            await update_waiting_message(message, "💾 جاري حفظ الدورات الإنجليزية...", user_id)

            # حفظ الدورات في الملف
            with open(Config.EN_COURSES_FILE, 'w', encoding='utf-8') as f:
                json.dump(courses, f, ensure_ascii=False, indent=2)

            # تحديث رسالة الانتظار
            await update_waiting_message(message, "📊 جاري تحليل النتائج...", user_id)

            # حساب الدورات الجديدة والنشطة
            new_courses = [course for course in courses if course.get('link') not in old_links]
            active_courses = [course for course in courses if course.get('coupons_left', 0) > 0]

            result_message = f"✅ تم تحديث الدورات الإنجليزية بنجاح!\n\n"
            result_message += f"🇺🇸 إجمالي الدورات: {len(courses)}\n"
            result_message += f"🆕 دورات جديدة: {len(new_courses)}\n"
            result_message += f"🎯 دورات نشطة: {len(active_courses)}\n"
            result_message += f"📄 تم فحص 5 صفحات من Comidoc\n"

            if len(new_courses) == 0:
                result_message += f"\n⚠️ لم يتم العثور على دورات جديدة"
            elif len(active_courses) == 0:
                result_message += f"\n❌ جميع الكوبونات منتهية الصلاحية"
            else:
                result_message += f"\n🎉 تم العثور على {len(new_courses)} دورة جديدة!"

            await update_waiting_message(message, result_message, user_id)
            # تسجيل وقت التحديث الناجح
            update_cooldown_manager.record_update()
        else:
            error_message = "❌ فشل في جلب الدورات الإنجليزية من الموقع\n🔄 حاول مرة أخرى لاحقاً"
            await update_waiting_message(message, error_message, user_id)

    except Exception as e:
        logger.error(f"خطأ في تحديث الدورات الإنجليزية: {e}")
        error_message = "❌ حدث خطأ في تحديث الدورات الإنجليزية"
        await update_waiting_message(message, error_message, user_id)

    finally:
        # إنهاء العملية وتحرير القفل
        user_operation_manager.end_operation(user_id)
        _update_lock.release()

async def button(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالجة الأزرار"""
    query = update.callback_query
    await query.answer()  # للرد على الضغطة

    # التحقق من صلاحيات المشرف للأوامر المقيدة
    admin_only_commands = ['update_courses', 'admin_panel', 'admin_stats', 'publish_channel', 'clean_expired', 'clear_cache', 'verify_coupons', 'manage_vip', 'send_message_channel', 'update_all_courses', 'clean_expired_vip']
    vip_allowed_commands = ['update_comidoc', 'update_discudemy', 'update_real_discount', 'update_en_courses']  # الأوامر المسموحة لأعضاء VIP

    # التحقق من الأوامر المقيدة للمشرفين فقط
    if query.data in admin_only_commands and update.effective_user.id not in ADMIN_IDS:
        await query.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        return

    # التحقق من الأوامر المسموحة لأعضاء VIP
    if query.data in vip_allowed_commands:
        if not vip_manager.is_vip(update.effective_user.id):
            await query.message.reply_text("⚠️ هذه الميزة متاحة لأعضاء VIP والمشرفين فقط")
            return

    # التحقق مما إذا كان الأمر هو إرسال دورة إلى القناة
    if query.data.startswith('send_channel_') or query.data.startswith('send_to_channel_'):
        # التحقق من صلاحيات المشرف أولاً
        if update.effective_user.id not in ADMIN_IDS:
            await query.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
            return

        # طباعة البيانات المرسلة للتصحيح
        logger.info(f"بيانات الزر: {query.data}")

        # استدعاء وظيفة إرسال الدورة إلى القناة مباشرة
        await send_to_channel_direct(update, context)
        return

    # معالجة طلب الاشتراك في خدمة VIP
    elif query.data == 'vip_subscribe':
        await handle_vip_subscribe(update, context)
        return

    # معالجة طلب عرض معلومات VIP
    elif query.data == 'vip_info':
        await handle_vip_info(update, context)
        return

    # معالجة طلب عرض دورات ديسكوديمي
    elif query.data == 'discudemy_courses':
        await show_discudemy_courses(update, context)
        return
    elif query.data == 'real_discount_courses':
        await show_real_discount_courses(update, context)
        return

    # معالجة الأوامر
    filters = None
    if query.data == 'ar_courses':
        filters = {'language': 'ar'}
    elif query.data == 'en_courses':
        # جلب الدورات الإنجليزية (5 صفحات فقط)
        await show_en_courses(update, context)
        return
    elif query.data == 'update_comidoc':
        await update_comidoc_courses(update, context)
        return
    elif query.data == 'update_discudemy':
        await update_discudemy_courses(update, context)
        return
    elif query.data == 'update_real_discount':
        await update_real_discount_courses(update, context)
        return
    elif query.data == 'update_all_courses':
        await update_all_courses(update, context)
        return
    elif query.data == 'admin_panel':
        await show_admin_panel(update, context)
        return
    elif query.data == 'publish_channel':
        await publish_to_channel(update, context)
        return
    elif query.data == 'admin_stats':
        await show_stats(update, context)
        return
    elif query.data == 'clean_expired':
        await clean_expired_courses(update, context)
        return
    elif query.data == 'clear_cache':
        await clear_cache(update, context)
        return
    elif query.data == 'verify_coupons':
        await verify_coupons_manually(update, context)
        return
    elif query.data == 'manage_vip':
        await show_vip_management(update, context)
        return
    elif query.data == 'send_message_channel':
        await show_send_message_form(update, context)
        return
    elif query.data == 'clean_expired_vip':
        await clean_expired_vip_manual(update, context)
        return
    elif query.data == 'update_comidoc_limited':
        await update_comidoc_limited(update, context)
        return
    elif query.data == 'update_en_limited':
        await update_en_limited(update, context)
        return

    elif query.data == 'toggle_notifications':
        await toggle_notifications_button(update, context)
        return
    elif query.data == 'test_auto_check':
        await test_auto_check_button(update, context)
        return
    elif query.data == 'cooldown_status':
        await cooldown_status_command(update, context)
        return
    # تم حذف معالجات العداد التنازلي - النظام الآن بسيط
    elif query.data == 'update_courses':
        await update_all_courses(update, context)
        return
    elif query.data == 'update_en_courses':
        await update_en_courses(update, context)
        return
    elif query.data == 'update_comidoc':
        await update_comidoc_courses(update, context)
        return
    elif query.data == 'update_en':
        await update_en_courses(update, context)
        return
    elif query.data == 'update_discudemy':
        await update_discudemy_courses(update, context)
        return
    elif query.data == 'update_real_discount':
        await update_real_discount_courses(update, context)
        return
    elif query.data == 'update_all':
        await update_all_courses(update, context)
        return

    # معالجة أزرار حذف أعضاء VIP
    elif query.data.startswith('remove_vip_'):
        await handle_remove_vip_button(update, context)
        return
    # تم إزالة معالج update_en_ - لم يعد مطلوباً

    # معالجة زر المعلومات
    elif query.data == 'info':
        await query.answer("ℹ️ هذا مجرد زر إعلامي", show_alert=True)
        return

    if filters is not None:
        await show_courses(update, context, filters)

# تم حذف دالة handle_update_en_link - لم تعد مطلوبة

async def handle_remove_vip_button(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالجة زر حذف عضو VIP"""
    query = update.callback_query

    # التحقق من صلاحيات المشرف
    if update.effective_user.id not in ADMIN_IDS:
        await query.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        return

    # استخراج معرف المستخدم من البيانات
    user_id_to_remove = query.data.replace('remove_vip_', '')

    try:
        user_id_to_remove = int(user_id_to_remove)

        # الحصول على معلومات المستخدم قبل الحذف
        vip_info = vip_manager.get_vip_info(user_id_to_remove)
        user_name = vip_info.get("name", "غير معروف") if vip_info else "غير معروف"

        # حذف المستخدم
        if vip_manager.remove_vip(user_id_to_remove):
            await query.message.reply_text(
                f"✅ تم حذف العضو بنجاح!\n\n"
                f"👤 الاسم: {user_name}\n"
                f"🆔 المعرف: {user_id_to_remove}\n"
                f"🗑️ تم الحذف في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )

            # تحديث قائمة إدارة VIP تلقائياً
            await show_vip_management(update, context)
        else:
            await query.message.reply_text(
                f"❌ فشل في حذف العضو!\n"
                f"المعرف: {user_id_to_remove}\n"
                f"قد يكون العضو غير موجود أو حدث خطأ في النظام."
            )

    except ValueError:
        await query.message.reply_text("❌ معرف المستخدم غير صالح")
    except Exception as e:
        logger.error(f"خطأ في حذف عضو VIP {user_id_to_remove}: {e}")
        await query.message.reply_text("❌ حدث خطأ غير متوقع أثناء حذف العضو")

async def clean_expired_vip_manual(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تنظيف أعضاء VIP المنتهية الصلاحية يدوياً"""
    # التحقق من صلاحيات المشرف
    if update.effective_user.id not in ADMIN_IDS:
        await update.callback_query.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        return

    message = await update.callback_query.message.reply_text("🧹 جاري تنظيف أعضاء VIP المنتهية الصلاحية...")

    try:
        # الحصول على جميع أعضاء VIP
        vip_users = vip_manager.get_all_vip_users()

        if not vip_users:
            await message.edit_text("ℹ️ لا يوجد أعضاء VIP للتحقق منهم")
            return

        expired_users = []
        current_date = datetime.now().date()

        # التحقق من كل عضو VIP
        for user_id, user_info in vip_users.items():
            try:
                expiry_date = user_info.get("expires_at", "")
                if expiry_date:
                    expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                    if expiry < current_date:
                        expired_users.append((user_id, user_info.get("name", "غير معروف")))
            except Exception as e:
                logger.error(f"خطأ في التحقق من عضو VIP {user_id}: {e}")

        if not expired_users:
            await message.edit_text("✅ لا يوجد أعضاء VIP منتهية الصلاحية للحذف")
            return

        # حذف الأعضاء المنتهية الصلاحية
        removed_count = 0
        removed_names = []

        for user_id, name in expired_users:
            if vip_manager.remove_vip(int(user_id)):
                removed_names.append(f"• {name} (ID: {user_id})")
                removed_count += 1
            else:
                logger.error(f"فشل في حذف عضو VIP: {name} (ID: {user_id})")

        if removed_count > 0:
            result_message = f"✅ تم حذف {removed_count} عضو VIP منتهي الصلاحية:\n\n"
            result_message += "\n".join(removed_names[:10])  # عرض أول 10 أعضاء فقط
            if len(removed_names) > 10:
                result_message += f"\n... و {len(removed_names) - 10} عضو آخر"
        else:
            result_message = "❌ فشل في حذف أي عضو VIP"

        await message.edit_text(result_message)

    except Exception as e:
        logger.error(f"خطأ في تنظيف أعضاء VIP المنتهية الصلاحية: {e}")
        await message.edit_text("❌ حدث خطأ أثناء تنظيف أعضاء VIP")

async def update_comidoc_limited(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تحديث دورات Comidoc للمستخدمين العاديين (محدود 3 مرات يومياً)"""
    user_id = update.effective_user.id

    # التحقق من أن المستخدم ليس VIP أو مشرف
    if vip_manager.is_vip(user_id) or is_admin(user_id):
        await update.callback_query.message.reply_text("⚠️ هذا الزر مخصص للمستخدمين العاديين فقط")
        return

    # التحقق من تضارب العمليات
    if await check_user_operation_conflict(update, 'update_courses'):
        return

    # التحقق من الحد اليومي
    can_update, _ = update_limit_manager.can_update(user_id)

    if not can_update:
        tomorrow = datetime.now().date() + timedelta(days=1)
        await update.callback_query.message.reply_text(
            f"⚠️ لقد استنفدت حدك اليومي من التحديثات (3 مرات)\n\n"
            f"🕐 سيتم إعادة تعيين الحد غداً: {tomorrow.strftime('%Y-%m-%d')}\n\n"
            f"💎 للحصول على تحديثات غير محدودة، اشترك في VIP:\n"
            f"👤 @GurusVIP\n"
            f"👤 معرف المستخدم: {user_id}"
        )
        return

    # استخدام مرة تحديث
    if not update_limit_manager.use_update(user_id):
        await update.callback_query.message.reply_text("❌ حدث خطأ في تسجيل استخدام التحديث")
        return

    # تحديث العداد المتبقي
    _, new_remaining = update_limit_manager.can_update(user_id)

    # إرسال رسالة انتظار
    message = await send_waiting_message(update, 'update_courses', user_id)

    # بدء العملية
    if not user_operation_manager.start_operation(user_id, 'update_comidoc_limited', message.message_id if message else None):
        await update.callback_query.message.reply_text("❌ فشل في بدء العملية، حاول مرة أخرى")
        return

    try:
        # تحديث رسالة الانتظار
        await update_waiting_message(message, "🔍 جاري قراءة الدورات الحالية...", user_id)

        # قراءة الدورات الحالية للمقارنة
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                old_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            old_courses = []

        old_links = {course.get('link') for course in old_courses}

        # تحديث رسالة الانتظار
        await update_waiting_message(message, "🌐 جاري جلب الدورات من المواقع...", user_id)

        # تحديث دورات Comidoc (الدورات العربية)
        courses = await fetch_all_courses('ar')

        if courses:
            # تحديث رسالة الانتظار
            await update_waiting_message(message, "📊 جاري تحليل النتائج...", user_id)

            # حساب الدورات الجديدة
            new_courses = [course for course in courses if course.get('link') not in old_links]
            active_courses = [course for course in courses if course.get('coupons_left', 0) > 0]

            result_message = f"✅ تم تحديث دورات Comidoc بنجاح!\n"
            result_message += f"📊 المتبقي اليوم: {new_remaining}/3 مرات\n\n"
            result_message += f"📈 إجمالي الدورات: {len(courses)}\n"
            result_message += f"🆕 دورات جديدة: {len(new_courses)}\n"
            result_message += f"🎯 دورات نشطة: {len(active_courses)}\n"

            if len(new_courses) == 0:
                result_message += f"\n⚠️ لم يتم العثور على دورات جديدة"
            elif len(active_courses) == 0:
                result_message += f"\n❌ جميع الكوبونات منتهية الصلاحية"
            else:
                result_message += f"\n🎉 تم العثور على {len(new_courses)} دورة جديدة!"

            result_message += f"\n\n💡 نصيحة: اشترك في VIP للحصول على تحديثات غير محدودة!"

            await update_waiting_message(message, result_message, user_id)
        else:
            error_message = (
                f"❌ فشل في جلب الدورات من الموقع\n"
                f"📊 المتبقي اليوم: {new_remaining}/3 مرات\n\n"
                f"🔄 حاول مرة أخرى لاحقاً"
            )
            await update_waiting_message(message, error_message, user_id)
    except Exception as e:
        logger.error(f"خطأ في تحديث دورات Comidoc للمستخدم العادي: {e}")
        error_message = (
            f"❌ حدث خطأ في تحديث دورات Comidoc\n"
            f"📊 المتبقي اليوم: {new_remaining}/3 مرات"
        )
        await update_waiting_message(message, error_message, user_id)
    finally:
        # إنهاء العملية
        user_operation_manager.end_operation(user_id)

async def update_en_limited(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تحديث الدورات الإنجليزية للمستخدمين العاديين (محدود 3 مرات يومياً)"""
    user_id = update.effective_user.id

    # التحقق من أن المستخدم ليس VIP أو مشرف
    if vip_manager.is_vip(user_id) or is_admin(user_id):
        await update.callback_query.message.reply_text("⚠️ هذا الزر مخصص للمستخدمين العاديين فقط")
        return

    # التحقق من تضارب العمليات
    if await check_user_operation_conflict(update, 'update_courses'):
        return

    # التحقق من الحد اليومي
    can_update, _ = update_limit_manager.can_update(user_id)

    if not can_update:
        tomorrow = datetime.now().date() + timedelta(days=1)
        await update.callback_query.message.reply_text(
            f"⚠️ لقد استنفدت حدك اليومي من التحديثات (3 مرات)\n\n"
            f"🕐 سيتم إعادة تعيين الحد غداً: {tomorrow.strftime('%Y-%m-%d')}\n\n"
            f"💎 للحصول على تحديثات غير محدودة، اشترك في VIP:\n"
            f"👤 @GurusVIP\n"
            f"👤 معرف المستخدم: {user_id}"
        )
        return

    # استخدام مرة تحديث
    if not update_limit_manager.use_update(user_id):
        await update.callback_query.message.reply_text("❌ حدث خطأ في تسجيل استخدام التحديث")
        return

    # تحديث العداد المتبقي
    _, new_remaining = update_limit_manager.can_update(user_id)

    # إرسال رسالة انتظار
    message = await send_waiting_message(update, 'update_courses', user_id)

    # بدء العملية
    if not user_operation_manager.start_operation(user_id, 'update_en_limited', message.message_id if message else None):
        await update.callback_query.message.reply_text("❌ فشل في بدء العملية، حاول مرة أخرى")
        return

    try:
        # تحديث رسالة الانتظار
        await update_waiting_message(message, "🔍 جاري قراءة الدورات الإنجليزية الحالية...", user_id)

        # قراءة الدورات الإنجليزية الحالية للمقارنة
        try:
            with open(Config.EN_COURSES_FILE, 'r', encoding='utf-8') as f:
                old_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            old_courses = []

        old_links = {course.get('link') for course in old_courses}

        # تحديث رسالة الانتظار
        await update_waiting_message(message, "🌐 جاري جلب الدورات الإنجليزية (5 صفحات)...", user_id)

        # تحديث الدورات الإنجليزية (5 صفحات فقط)
        courses = []
        for page in range(1, 6):  # صفحات 1-5
            await update_waiting_message(message, f"📄 جاري فحص الصفحة {page}/5 للدورات الإنجليزية...", user_id)
            logger.info(f"جاري فحص الصفحة {page} للدورات الإنجليزية...")
            page_courses = await process_page(page, 'en')
            courses.extend(page_courses)
            logger.info(f"تم العثور على {len(page_courses)} دورة في الصفحة {page}")

        if courses:
            # تحديث رسالة الانتظار
            await update_waiting_message(message, "💾 جاري حفظ الدورات الإنجليزية...", user_id)

            # حفظ الدورات في الملف
            with open(Config.EN_COURSES_FILE, 'w', encoding='utf-8') as f:
                json.dump(courses, f, ensure_ascii=False, indent=2)

            # تحديث رسالة الانتظار
            await update_waiting_message(message, "📊 جاري تحليل النتائج...", user_id)

            # حساب الدورات الجديدة والنشطة
            new_courses = [course for course in courses if course.get('link') not in old_links]
            active_courses = [course for course in courses if course.get('coupons_left', 0) > 0]

            result_message = f"✅ تم تحديث الدورات الإنجليزية بنجاح!\n"
            result_message += f"📊 المتبقي اليوم: {new_remaining}/3 مرات\n\n"
            result_message += f"🇺🇸 إجمالي الدورات: {len(courses)}\n"
            result_message += f"🆕 دورات جديدة: {len(new_courses)}\n"
            result_message += f"🎯 دورات نشطة: {len(active_courses)}\n"

            if len(new_courses) == 0:
                result_message += f"\n⚠️ لم يتم العثور على دورات جديدة"
            elif len(active_courses) == 0:
                result_message += f"\n❌ جميع الكوبونات منتهية الصلاحية"
            else:
                result_message += f"\n🎉 تم العثور على {len(new_courses)} دورة جديدة!"

            result_message += f"\n\n💡 نصيحة: اشترك في VIP للحصول على تحديثات غير محدودة!"

            await update_waiting_message(message, result_message, user_id)
        else:
            error_message = (
                f"❌ فشل في جلب الدورات الإنجليزية من الموقع\n"
                f"📊 المتبقي اليوم: {new_remaining}/3 مرات\n\n"
                f"🔄 حاول مرة أخرى لاحقاً"
            )
            await update_waiting_message(message, error_message, user_id)
    except Exception as e:
        logger.error(f"خطأ في تحديث الدورات الإنجليزية للمستخدم العادي: {e}")
        error_message = (
            f"❌ حدث خطأ في تحديث الدورات الإنجليزية\n"
            f"📊 المتبقي اليوم: {new_remaining}/3 مرات"
        )
        await update_waiting_message(message, error_message, user_id)
    finally:
        # إنهاء العملية
        user_operation_manager.end_operation(user_id)

async def update_comidoc_courses(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تحديث دورات Comidoc فقط"""
    user_id = update.effective_user.id

    # التحقق من التوقيت بين التحديثات (3 دقائق لأي تحديث)
    can_update, remaining_seconds = update_cooldown_manager.can_update()
    if not can_update:
        remaining_time = update_cooldown_manager.get_remaining_time_text(remaining_seconds)
        last_update_info = update_cooldown_manager.get_last_update_info()
        await update.callback_query.message.reply_text(
            f"⏰ يجب الانتظار {remaining_time} قبل أي تحديث\n\n"
            f"📊 آخر تحديث: {last_update_info}\n"
            f"🔄 هذا النظام يمنع التحديثات المتتالية التي تسبب مشاكل للمستخدمين الآخرين\n"
            f"⏱️ الحد الأدنى بين أي تحديث: 3 دقائق"
        )
        return

    # التحقق من تضارب العمليات
    if await check_user_operation_conflict(update, 'update_courses'):
        return

    # التحقق من وجود عملية تحديث أخرى
    if not _update_lock.acquire(blocking=False):
        await update.callback_query.message.reply_text("⏸️ عملية تحديث أخرى قيد التشغيل، يرجى المحاولة لاحقاً")
        return

    # إرسال رسالة انتظار
    message = await send_waiting_message(update, 'update_courses', user_id)

    # بدء العملية
    if not user_operation_manager.start_operation(user_id, 'update_comidoc_courses', message.message_id if message else None):
        await update.callback_query.message.reply_text("❌ فشل في بدء العملية، حاول مرة أخرى")
        _update_lock.release()
        return

    try:
        # تحديث رسالة الانتظار
        await update_waiting_message(message, "🔍 جاري قراءة الدورات الحالية...", user_id)

        # قراءة الدورات الحالية للمقارنة
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                old_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            old_courses = []

        old_links = {course.get('link') for course in old_courses}

        # تحديث رسالة الانتظار
        await update_waiting_message(message, "🌐 جاري جلب الدورات العربية من Comidoc...", user_id)

        # جلب الدورات العربية من Comidoc
        ar_courses = await fetch_all_courses('ar')

        if ar_courses:
            # تحديث رسالة الانتظار
            await update_waiting_message(message, "📊 جاري تحليل النتائج...", user_id)

            # حساب الدورات الجديدة والنشطة
            new_courses = [course for course in ar_courses if course.get('link') not in old_links]
            active_courses = [course for course in ar_courses if course.get('coupons_left', 0) > 0]

            result_message = f"✅ تم تحديث دورات Comidoc بنجاح!\n\n"
            result_message += f"📈 إجمالي الدورات: {len(ar_courses)}\n"
            result_message += f"🆕 دورات جديدة: {len(new_courses)}\n"
            result_message += f"🎯 دورات نشطة: {len(active_courses)}\n"

            if len(new_courses) == 0:
                result_message += f"\n⚠️ لم يتم العثور على دورات جديدة"
            elif len(active_courses) == 0:
                result_message += f"\n❌ جميع الكوبونات منتهية الصلاحية"
            else:
                result_message += f"\n🎉 تم العثور على {len(new_courses)} دورة جديدة!"

            await update_waiting_message(message, result_message, user_id)
            # تسجيل وقت التحديث الناجح
            update_cooldown_manager.record_update()
        else:
            error_message = "❌ فشل في جلب الدورات من الموقع\n🔄 حاول مرة أخرى لاحقاً"
            await update_waiting_message(message, error_message, user_id)

    except Exception as e:
        logger.error(f"خطأ في تحديث دورات Comidoc: {e}")
        error_message = "❌ حدث خطأ في تحديث دورات Comidoc"
        await update_waiting_message(message, error_message, user_id)

    finally:
        # إنهاء العملية وتحرير القفل
        user_operation_manager.end_operation(user_id)
        _update_lock.release()

async def update_discudemy_courses(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تحديث دورات Discudemy فقط"""

    # التحقق من التوقيت بين التحديثات (3 دقائق لأي تحديث)
    can_update, remaining_seconds = update_cooldown_manager.can_update()
    if not can_update:
        remaining_time = update_cooldown_manager.get_remaining_time_text(remaining_seconds)
        last_update_info = update_cooldown_manager.get_last_update_info()
        await update.callback_query.message.reply_text(
            f"⏰ يجب الانتظار {remaining_time} قبل أي تحديث\n\n"
            f"📊 آخر تحديث: {last_update_info}\n"
            f"🔄 هذا النظام يمنع التحديثات المتتالية التي تسبب مشاكل للمستخدمين الآخرين\n"
            f"⏱️ الحد الأدنى بين أي تحديث: 3 دقائق"
        )
        return

    message = await update.callback_query.message.reply_text("🔄 جاري تحديث دورات Discudemy...")

    try:
        # جلب دورات ديسكوديمي
        await fetch_discudemy_courses()

        # عرض نتائج التحديث
        await message.edit_text("✅ تم تحديث دورات Discudemy بنجاح!")
        # تسجيل وقت التحديث الناجح
        update_cooldown_manager.record_update()

    except Exception as e:
        logger.error(f"خطأ في تحديث دورات Discudemy: {e}")
        await message.edit_text("❌ حدث خطأ في تحديث دورات Discudemy")

async def update_real_discount_courses(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تحديث دورات Real Discount فقط"""

    # التحقق من التوقيت بين التحديثات (3 دقائق لأي تحديث)
    can_update, remaining_seconds = update_cooldown_manager.can_update()
    if not can_update:
        remaining_time = update_cooldown_manager.get_remaining_time_text(remaining_seconds)
        last_update_info = update_cooldown_manager.get_last_update_info()
        await update.callback_query.message.reply_text(
            f"⏰ يجب الانتظار {remaining_time} قبل أي تحديث\n\n"
            f"📊 آخر تحديث: {last_update_info}\n"
            f"🔄 هذا النظام يمنع التحديثات المتتالية التي تسبب مشاكل للمستخدمين الآخرين\n"
            f"⏱️ الحد الأدنى بين أي تحديث: 3 دقائق"
        )
        return

    message = await update.callback_query.message.reply_text("🔄 جاري تحديث دورات Real Discount...")

    try:
        # جلب دورات Real Discount
        await fetch_real_discount_courses()

        # عرض نتائج التحديث
        await message.edit_text("✅ تم تحديث دورات Real Discount بنجاح!")
        # تسجيل وقت التحديث الناجح
        update_cooldown_manager.record_update()

    except Exception as e:
        logger.error(f"خطأ في تحديث دورات Real Discount: {e}")
        await message.edit_text("❌ حدث خطأ في تحديث دورات Real Discount")

async def update_all_courses(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تحديث جميع الدورات (من جميع المواقع)"""

    # التحقق من التوقيت بين التحديثات (3 دقائق لأي تحديث)
    can_update, remaining_seconds = update_cooldown_manager.can_update()
    if not can_update:
        remaining_time = update_cooldown_manager.get_remaining_time_text(remaining_seconds)
        last_update_info = update_cooldown_manager.get_last_update_info()
        await update.callback_query.message.reply_text(
            f"⏰ يجب الانتظار {remaining_time} قبل أي تحديث\n\n"
            f"📊 آخر تحديث: {last_update_info}\n"
            f"🔄 هذا النظام يمنع التحديثات المتتالية التي تسبب مشاكل للمستخدمين الآخرين\n"
            f"⏱️ الحد الأدنى بين أي تحديث: 3 دقائق"
        )
        return

    # التحقق من وجود عملية تحديث أخرى
    if not _update_lock.acquire(blocking=False):
        await update.callback_query.message.reply_text("⏸️ عملية تحديث أخرى قيد التشغيل، يرجى المحاولة لاحقاً")
        return

    message = await update.callback_query.message.reply_text("🔄 جاري تحديث جميع الدورات...")

    try:
        # جلب الدورات العربية من Comidoc
        await fetch_all_courses('ar')

        # جلب دورات ديسكوديمي
        await fetch_discudemy_courses()

        # جلب دورات Real Discount
        await fetch_real_discount_courses()

        # عرض نتائج التحديث
        await message.edit_text("✅ تم تحديث جميع الدورات بنجاح!")
        # تسجيل وقت التحديث الناجح
        update_cooldown_manager.record_update()

    except Exception as e:
        logger.error(f"خطأ في تحديث جميع الدورات: {e}")
        await message.edit_text("❌ حدث خطأ في تحديث جميع الدورات")

    finally:
        # تحرير القفل
        _update_lock.release()

async def show_admin_panel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض لوحة المشرف"""
    # إنشاء أزرار متجاورة للوحة المشرف
    row1 = [
        InlineKeyboardButton("📊 إحصائيات", callback_data='admin_stats'),
        InlineKeyboardButton("📢 نشر في القناة", callback_data='publish_channel')
    ]

    row2 = [
        InlineKeyboardButton("🧹 تنظيف الكوبونات", callback_data='clean_expired'),
        InlineKeyboardButton("🔄 تحديث الدورات", callback_data='update_courses')
    ]

    row3 = [
        InlineKeyboardButton("🔍 التحقق من الكوبونات", callback_data='verify_coupons'),
        InlineKeyboardButton("🗑️ تنظيف الذاكرة", callback_data='clear_cache')
    ]

    row4 = [
        InlineKeyboardButton("👥 إدارة أعضاء VIP", callback_data='manage_vip'),
        InlineKeyboardButton("📤 إرسال رسالة للقناة", callback_data='send_message_channel')
    ]

    row5 = [
        InlineKeyboardButton("🧹 تنظيف VIP المنتهية", callback_data='clean_expired_vip'),
        InlineKeyboardButton("🧪 اختبار الفحص التلقائي", callback_data='test_auto_check')
    ]

    # زر تفعيل/إيقاف الإشعارات
    notification_status = "🔔 إيقاف الإشعارات" if AUTO_CHECK_NOTIFICATIONS else "🔕 تفعيل الإشعارات"
    row6 = [
        InlineKeyboardButton(notification_status, callback_data='toggle_notifications')
    ]

    # زر حالة نظام التحكم في التوقيت
    row7 = [
        InlineKeyboardButton("⏰ حالة نظام التوقيت", callback_data='cooldown_status')
    ]

    keyboard = [row1, row2, row3, row4, row5, row6, row7]

    # إضافة حالة الإشعارات في النص
    notifications_status_text = "مفعلة ✅" if AUTO_CHECK_NOTIFICATIONS else "معطلة ❌"

    await update.callback_query.message.reply_text(
        f"⚙️ **لوحة المشرف**\n\n"
        f"🔔 إشعارات الفحص التلقائي: {notifications_status_text}\n\n"
        f"اختر إحدى العمليات:",
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def publish_to_channel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """نشر الدورات في القناة"""
    if not await admin_only(update, context):
        return

    message = await update.callback_query.message.reply_text("جاري نشر الدورات في القناة...")

    try:
        # قراءة الدورات من ملف comidoc
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                comidoc_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            comidoc_courses = []

        # قراءة الدورات من ملف discudemy
        try:
            with open(Config.DISCUDEMY_COURSES_FILE, 'r', encoding='utf-8') as f:
                discudemy_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            discudemy_courses = []

        # قراءة الدورات من ملف real.discount
        try:
            with open(Config.REAL_DISCOUNT_COURSES_FILE, 'r', encoding='utf-8') as f:
                real_discount_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            real_discount_courses = []

        # دمج جميع الدورات
        all_courses = comidoc_courses + discudemy_courses + real_discount_courses
        logger.info(f"تم تحميل {len(comidoc_courses)} دورة من comidoc و {len(discudemy_courses)} دورة من discudemy و {len(real_discount_courses)} دورة من real.discount")

        # قراءة الدورات المرسلة سابقًا
        try:
            with open(Config.SENT_COURSES_FILE, 'r', encoding='utf-8') as f:
                sent_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            sent_courses = []

        # الحصول على روابط الدورات المرسلة سابقًا
        sent_links = [course.get('link') for course in sent_courses]

        # تصفية الدورات التي لم يتم إرسالها بعد ولديها كوبونات متبقية
        new_courses = [
            course for course in all_courses
            if course.get('link') not in sent_links
            and course.get('coupons_left', 0) > 0
        ]

        if not new_courses:
            await message.edit_text("❌ لا توجد دورات جديدة للنشر")
            return

        # نشر الدورات الجديدة (بحد أقصى 5 دورات)
        courses_to_publish = new_courses[:5]
        published_count = 0

        for course in courses_to_publish:
            try:
                # تحضير نص الرسالة بالتنسيق الاحترافي
                language_flag = "🇸🇦" if course.get('language') == 'ar' else "🇺🇸"

                # إضافة إيموجي عشوائي للعنوان
                title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
                random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
                random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

                # تحديد سعر الدورة الأصلي من البيانات المستخرجة
                original_price = course.get('original_price', "49.99$")

                # تحديد تاريخ النشر (التاريخ الحالي)
                current_date = datetime.now().strftime("%Y-%m-%d")

                message_text = (
                    f"{random_emoji1} كورس جديد متاح مجاناً! {random_emoji2}\n\n"
                    f"📝 {course['title']}\n\n"
                    f"💰 السعر الأصلي: {original_price}\n"
                    f"🎁 السعر الآن: مجاني (خصم 100%)\n"
                    f"🔑 كوبون: مفعل تلقائي\n"
                    f"🏢 المنصة: Udemy\n"
                    f"📅 تاريخ النشر: {current_date}\n"
                    f"👥 الكوبونات المتبقية: {course.get('coupons_left', '100')}\n"
                    f"🌐 اللغة: {language_flag}\n\n"
                    f"⚡ سارع بالتسجيل قبل انتهاء العرض! ⚡"
                )

                # إنشاء أزرار
                keyboard = [
                    [InlineKeyboardButton("🔥 الحصول على الكوبون 🔥", url=course['link'])],
                    [InlineKeyboardButton("📢 قناة الدورات", url='https://t.me/dqw45dd')]
                ]

                if CHANNEL_USERNAME:
                    keyboard.append([InlineKeyboardButton("🔔 اشترك في القناة", url=f"https://t.me/{CHANNEL_USERNAME}")])

                reply_markup = InlineKeyboardMarkup(keyboard)

                # استخدام معرف القناة الصحيح
                channel_id = -1002613463650  # معرف القناة الصحيح

                # إرسال الرسالة إلى القناة مع الصورة إذا كانت متوفرة
                if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
                    # إرسال الرسالة مع الصورة
                    await context.bot.send_photo(
                        chat_id=channel_id,
                        photo=course['thumbnail'],
                        caption=message_text,
                        reply_markup=reply_markup,
                        parse_mode=None  # عدم استخدام أي تنسيق لتجنب الأخطاء
                    )
                else:
                    # إرسال الرسالة بدون صورة
                    await context.bot.send_message(
                        chat_id=channel_id,
                        text=message_text,
                        reply_markup=reply_markup,
                        disable_web_page_preview=True,
                        parse_mode=None  # عدم استخدام أي تنسيق لتجنب الأخطاء
                    )

                # إضافة الدورة إلى قائمة الدورات المرسلة
                course['sent_date'] = datetime.now(timezone.utc).isoformat()
                sent_courses.append(course)
                published_count += 1

                # انتظار قليلاً لتجنب تجاوز حدود التليجرام
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"خطأ في نشر الدورة {course.get('title')}: {e}")

        # حفظ الدورات المرسلة
        with open(Config.SENT_COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(sent_courses, f, ensure_ascii=False, indent=2)

        await message.edit_text(f"✅ تم نشر {published_count} دورة في القناة بنجاح")

    except Exception as e:
        logger.error(f"خطأ في نشر الدورات: {e}")
        await message.edit_text("❌ حدث خطأ في نشر الدورات")

async def show_stats(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض إحصائيات الدورات"""
    if not await admin_only(update, context):
        return

    message = await update.callback_query.message.reply_text("جاري جمع الإحصائيات...")

    try:
        # قراءة الدورات من الملف
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                all_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            all_courses = []

        # قراءة دورات ديسكوديمي
        try:
            with open(Config.DISCUDEMY_COURSES_FILE, 'r', encoding='utf-8') as f:
                discudemy_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            discudemy_courses = []

        # قراءة الدورات المرسلة سابقًا
        try:
            with open(Config.SENT_COURSES_FILE, 'r', encoding='utf-8') as f:
                sent_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            sent_courses = []

        # حساب الإحصائيات
        total_comidoc_courses = len(all_courses)
        ar_courses = len([c for c in all_courses if c.get('language') == 'ar'])

        total_discudemy_courses = len(discudemy_courses)
        active_discudemy_courses = len([c for c in discudemy_courses if c.get('coupons_left', 0) > 0])

        active_comidoc_courses = len([c for c in all_courses if c.get('coupons_left', 0) > 0])
        sent_count = len(sent_courses)

        total_all_courses = total_comidoc_courses + total_discudemy_courses
        total_active_courses = active_comidoc_courses + active_discudemy_courses

        # تحضير نص الإحصائيات
        stats_text = (
            "📊 إحصائيات الدورات\n\n"
            f"إجمالي الدورات: {total_all_courses}\n"
            f"🇸🇦 دورات عربية (comidoc): {ar_courses}\n"
            f"💰 دورات ديسكوديمي المدفوعة: {total_discudemy_courses}\n"
            f"✅ دورات نشطة: {total_active_courses}\n"
            f"📢 دورات تم نشرها: {sent_count}\n\n"
            f"تفاصيل الدورات النشطة:\n"
            f"- دورات comidoc: {active_comidoc_courses}\n"
            f"- دورات ديسكوديمي: {active_discudemy_courses}\n"
        )

        await message.edit_text(stats_text)

    except Exception as e:
        logger.error(f"خطأ في عرض الإحصائيات: {e}")
        await message.edit_text("❌ حدث خطأ في جمع الإحصائيات")

async def show_vip_management(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض واجهة إدارة أعضاء VIP بتنسيق جدول احترافي"""
    if not await admin_only(update, context):
        return

    # قراءة قائمة أعضاء VIP
    vip_users = vip_manager.get_all_vip_users()

    # إنشاء رسالة تعرض قائمة الأعضاء بتنسيق جدول
    message_text = "� <b>لوحة إدارة أعضاء VIP</b>\n\n"

    if vip_users:
        # إنشاء رأس الجدول
        message_text += "<pre>┌─────────────────────────────────────────────────────┐\n"
        message_text += "│ المعرف       │ الاسم           │ الحالة  │ تاريخ الانتهاء   │\n"
        message_text += "├─────────────────────────────────────────────────────┤\n"

        # إضافة بيانات الأعضاء مع أزرار الحذف
        vip_list = list(vip_users.items())
        for user_id, user_info in vip_list:
            name = user_info.get("name", "غير معروف")
            # اقتصار الاسم على 15 حرفًا كحد أقصى
            if len(name) > 15:
                name = name[:12] + "..."

            expiry_date = user_info.get("expires_at", "غير معروف")

            # حساب الأيام المتبقية والحالة
            try:
                expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                days_left = (expiry - datetime.now().date()).days
                if days_left > 0:
                    status = "✅ نشط"
                    days_text = f"({days_left} يوم)"
                else:
                    status = "❌ منتهي"
                    days_text = "(منتهي)"
            except:
                status = "❓"
                days_text = "(غير معروف)"

            # تنسيق المعرف ليكون بطول ثابت
            user_id_str = str(user_id)
            if len(user_id_str) > 10:
                user_id_str = user_id_str[:7] + "..."

            # إضافة الصف إلى الجدول
            message_text += f"│ {user_id_str:<10} │ {name:<15} │ {status:<6} │ {expiry_date} {days_text:<8} │\n"

        # إغلاق الجدول
        message_text += "└─────────────────────────────────────────────────────┘</pre>\n\n"

        # إضافة إحصائيات
        active_users = sum(1 for _, info in vip_users.items() if
                          datetime.strptime(info.get("expires_at", "2000-01-01"), "%Y-%m-%d").date() > datetime.now().date())

        message_text += f"<b>📊 الإحصائيات:</b>\n"
        message_text += f"• إجمالي الأعضاء: {len(vip_users)}\n"
        message_text += f"• الأعضاء النشطين: {active_users}\n"
        message_text += f"• الاشتراكات المنتهية: {len(vip_users) - active_users}\n\n"
    else:
        message_text += "<i>لا يوجد أعضاء VIP حالياً.</i>\n\n"

    # إضافة أزرار للإدارة
    message_text += "<b>🛠️ إدارة الأعضاء:</b>\n\n"
    message_text += "• <b>إضافة عضو جديد:</b>\n"
    message_text += "<code>/add_vip معرف_المستخدم عدد_الأيام الاسم</code>\n"
    message_text += "<b>مثال:</b> <code>/add_vip 123456789 30 محمد أحمد</code>\n\n"
    message_text += "• <b>حذف عضو:</b>\n"
    message_text += "<code>/remove_vip معرف_المستخدم</code>\n"
    message_text += "<b>مثال:</b> <code>/remove_vip 123456789</code>\n\n"
    message_text += "• <b>تمديد اشتراك:</b>\n"
    message_text += "<code>/extend_vip معرف_المستخدم عدد_الأيام</code>\n"
    message_text += "<b>مثال:</b> <code>/extend_vip 123456789 30</code>"

    # إنشاء أزرار لإدارة الأعضاء
    keyboard = []

    # إضافة زر حذف أمام كل عضو VIP
    if vip_users:
        vip_list = list(vip_users.items())

        # إضافة زر حذف لكل عضو في صف منفصل
        for user_id, user_info in vip_list:
            name = user_info.get("name", "غير معروف")
            # اقتصار الاسم على 15 حرفًا كحد أقصى
            if len(name) > 15:
                name = name[:12] + "..."

            # حساب الأيام المتبقية
            try:
                expiry_date = user_info.get("expires_at", "غير معروف")
                expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                days_left = (expiry - datetime.now().date()).days
                if days_left > 0:
                    status_emoji = "✅"
                    days_text = f"({days_left} يوم)"
                elif days_left == 0:
                    status_emoji = "⚠️"
                    days_text = "(ينتهي اليوم)"
                else:
                    status_emoji = "❌"
                    days_text = "(منتهي)"
            except:
                status_emoji = "❓"
                days_text = "(غير معروف)"

            # إنشاء زر لكل عضو مع معلوماته وزر الحذف
            keyboard.append([
                InlineKeyboardButton(
                    f"{status_emoji} {name} {days_text}",
                    callback_data='info'
                ),
                InlineKeyboardButton(
                    "🗑️ حذف",
                    callback_data=f'remove_vip_{user_id}'
                )
            ])

        # إضافة فاصل إذا كان هناك أكثر من 15 عضو
        if len(vip_list) > 15:
            keyboard = keyboard[:15]  # عرض أول 15 عضو فقط
            keyboard.append([InlineKeyboardButton("⚠️ يتم عرض أول 15 عضو فقط", callback_data='info')])

    # أزرار التحكم الرئيسية
    keyboard.extend([
        [InlineKeyboardButton("🔄 تحديث القائمة", callback_data='manage_vip')],
        [InlineKeyboardButton("🔙 العودة للوحة المشرف", callback_data='admin_panel')]
    ])

    await update.callback_query.message.reply_text(
        message_text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='HTML'
    )

async def show_send_message_form(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض نموذج إرسال رسالة إلى القناة"""
    if not await admin_only(update, context):
        return

    message_text = """
📤 <b>إرسال رسالة إلى القناة</b>

لإرسال رسالة إلى القناة، استخدم الأمر التالي:
<code>/send_to_channel الرسالة التي تريد إرسالها</code>

<b>ملاحظات:</b>
• يمكنك استخدام تنسيق HTML في الرسالة
• يمكنك إضافة روابط وإيموجي
• الرسالة ستظهر كما هي في القناة
    """

    # إنشاء أزرار للعودة إلى لوحة المشرف
    keyboard = [[InlineKeyboardButton("🔙 العودة للوحة المشرف", callback_data='admin_panel')]]

    await update.callback_query.message.reply_text(
        message_text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='HTML'
    )

async def clean_expired_courses(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تنظيف الكوبونات المنتهية"""
    if not await admin_only(update, context):
        return

    message = await update.callback_query.message.reply_text("جاري تنظيف الكوبونات المنتهية...")

    try:
        # قراءة الدورات من الملف
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                all_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            all_courses = []

        # عدد الدورات قبل التنظيف
        before_count = len(all_courses)

        # تصفية الدورات التي لديها كوبونات متبقية فقط
        active_courses = [course for course in all_courses if course.get('coupons_left', 0) > 0]

        # عدد الدورات بعد التنظيف
        after_count = len(active_courses)
        removed_count = before_count - after_count

        # حفظ الدورات النشطة
        with open(Config.COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(active_courses, f, ensure_ascii=False, indent=2)

        await message.edit_text(
            f"✅ تم تنظيف الكوبونات المنتهية بنجاح\n"
            f"تم إزالة {removed_count} دورة منتهية\n"
            f"الدورات النشطة المتبقية: {after_count}"
        )

    except Exception as e:
        logger.error(f"خطأ في تنظيف الكوبونات المنتهية: {e}")
        await message.edit_text("❌ حدث خطأ في تنظيف الكوبونات المنتهية")

def run_async_task(coroutine):
    """تشغيل مهمة غير متزامنة في حلقة أحداث جديدة"""
    try:
        # محاولة استخدام الحلقة الحالية إذا كانت متاحة
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # إذا كانت الحلقة تعمل، استخدم thread pool
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, coroutine)
                return future.result()
        else:
            return loop.run_until_complete(coroutine)
    except RuntimeError:
        # إنشاء حلقة جديدة إذا لم تكن هناك حلقة
        return asyncio.run(coroutine)

def update_ar_courses_job():
    """وظيفة تحديث الدورات العربية للمجدول"""
    run_async_task(fetch_all_courses('ar'))

def clean_old_update_limits():
    """تنظيف بيانات حدود التحديث القديمة (أكثر من 7 أيام)"""
    try:
        current_date = datetime.now().date()
        cutoff_date = current_date - timedelta(days=7)

        old_data = update_limit_manager.limits_data.copy()
        cleaned_count = 0

        for user_id, user_data in old_data.items():
            try:
                user_date = datetime.fromisoformat(user_data.get('date', '2000-01-01')).date()
                if user_date < cutoff_date:
                    del update_limit_manager.limits_data[user_id]
                    cleaned_count += 1
            except:
                # حذف البيانات التالفة
                del update_limit_manager.limits_data[user_id]
                cleaned_count += 1

        if cleaned_count > 0:
            update_limit_manager._save_limits_data()
            logger.info(f"تم تنظيف {cleaned_count} سجل قديم من بيانات حدود التحديث")

    except Exception as e:
        logger.error(f"خطأ في تنظيف بيانات حدود التحديث: {e}")

async def auto_check_new_courses():
    """فحص تلقائي للدورات الجديدة كل 10 دقائق"""
    current_time = datetime.now().strftime('%H:%M:%S')

    # التحقق من وجود عملية فحص أخرى قيد التشغيل
    if not _update_lock.acquire(blocking=False):
        logger.info(f"⏸️ [{current_time}] تم تخطي الفحص التلقائي - عملية فحص أخرى قيد التشغيل")
        return

    try:
        logger.info(f"🔍 [{current_time}] بدء الفحص التلقائي للدورات الجديدة...")

        # الفحص يعمل في صمت - لا إشعار بدء الفحص
        # قراءة الدورات العربية الحالية
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                current_ar_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            current_ar_courses = []

        # الحصول على روابط الدورات العربية الحالية
        current_ar_links = {course.get('link') for course in current_ar_courses}

        # فحص 8 صفحات من الدورات العربية فقط
        new_ar_courses = []
        for page in range(1, 9):  # صفحات 1-8
            page_courses = await process_page(page, 'ar')
            new_ar_courses.extend(page_courses)

        # تصفية الدورات العربية الجديدة فقط
        truly_new_courses = []
        for course in new_ar_courses:
            if course.get('link') not in current_ar_links:
                # التحقق من كون الدورة محظورة
                if not is_course_blocked(course.get('link', ''), course.get('title', '')):
                    truly_new_courses.append(course)

        if truly_new_courses:
            logger.info(f"🆕 تم العثور على {len(truly_new_courses)} دورة عربية جديدة!")

            # حفظ الدورات العربية الجديدة
            all_ar_courses = current_ar_courses + truly_new_courses
            with open(Config.COURSES_FILE, 'w', encoding='utf-8') as f:
                json.dump(all_ar_courses, f, ensure_ascii=False, indent=2)

            # إرسال تنبيه للمشرف وإرسال للقناة
            await send_new_course_notifications(truly_new_courses)

            # إشعار فقط عند وجود دورات جديدة
            try:
                if AUTO_CHECK_NOTIFICATIONS and ADMIN_IDS:
                    application = Application.builder().token(TOKEN).build()
                    await application.bot.send_message(
                        chat_id=ADMIN_IDS[0],
                        text=f"🆕 **دورات جديدة!** - {current_time}\n📚 تم العثور على {len(truly_new_courses)} دورة جديدة!",
                        parse_mode='Markdown'
                    )
            except Exception as notify_error:
                logger.error(f"خطأ في إرسال إشعار الدورات الجديدة: {notify_error}")
        else:
            logger.info("ℹ️ لا توجد دورات عربية جديدة")

            # لا إشعار عند عدم وجود دورات جديدة - العمل في صمت
            pass

    except Exception as e:
        logger.error(f"❌ خطأ في الفحص التلقائي: {e}")

        # إشعار خطأ في الفحص
        try:
            if ADMIN_IDS:
                application = Application.builder().token(TOKEN).build()
                await application.bot.send_message(
                    chat_id=ADMIN_IDS[0],
                    text=f"❌ **خطأ في الفحص** - {current_time}\n{str(e)}",
                    parse_mode='Markdown'
                )
        except Exception as notify_error:
            logger.error(f"خطأ في إرسال إشعار الخطأ: {notify_error}")

    finally:
        # تحرير القفل في جميع الحالات
        _update_lock.release()
        logger.debug(f"🔓 [{current_time}] تم تحرير قفل الفحص التلقائي")

async def send_new_course_notifications(new_courses):
    """إرسال تنبيهات الدورات الجديدة للمشرف والقناة"""
    try:
        # إنشاء تطبيق البوت للإرسال
        application = Application.builder().token(TOKEN).build()

        for course in new_courses[:3]:  # أول 3 دورات فقط لتجنب الإزعاج
            try:
                # إرسال تنبيه للمشرف الأول (بالرابط الأصلي)
                admin_id = ADMIN_IDS[0] if ADMIN_IDS else None
                if admin_id:
                    admin_message = (
                        f"🆕 **دورة جديدة تم اكتشافها!**\n\n"
                        f"📝 {course['title']}\n"
                        f"💰 السعر: {course.get('original_price', 'غير محدد')}\n"
                        f"🔗 الرابط: {course['link']}\n"
                        f"⏰ تم الاكتشاف: {datetime.now().strftime('%H:%M:%S')}"
                    )

                    await application.bot.send_message(
                        chat_id=admin_id,
                        text=admin_message,
                        parse_mode='Markdown',
                        disable_web_page_preview=True
                    )

                # إرسال للقناة
                await send_course_to_channel(course, application.bot)

                # انتظار قصير بين الرسائل
                await asyncio.sleep(2)

            except Exception as e:
                logger.error(f"خطأ في إرسال تنبيه الدورة {course.get('title', 'غير معروف')}: {e}")

    except Exception as e:
        logger.error(f"خطأ في إرسال التنبيهات: {e}")

async def send_course_to_channel(course, bot):
    """إرسال دورة واحدة للقناة"""
    try:
        # التحقق من صلاحيات البوت في القناة
        try:
            chat_member = await bot.get_chat_member(CHANNEL_ID, bot.id)
            if chat_member.status not in ['administrator', 'creator']:
                logger.error(f"البوت ليس مشرفاً في القناة {CHANNEL_ID}")
                return
        except Exception as perm_error:
            logger.error(f"خطأ في التحقق من صلاحيات القناة: {perm_error}")
            return
        # تحضير رسالة الدورة
        title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
        random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
        random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

        original_price = course.get('original_price', "49.99$")
        current_date = datetime.now().strftime("%Y-%m-%d")

        message_text = (
            f"{random_emoji1} كورس جديد متاح مجاناً! {random_emoji2}\n\n"
            f"📝 {course['title']}\n\n"
            f"💰 السعر الأصلي: {original_price}\n"
            f"🎁 السعر الآن: مجاني (خصم 100%)\n"
            f"🔑 كوبون: مفعل تلقائي\n"
            f"🏢 المنصة: Udemy\n"
            f"📅 تاريخ النشر: {current_date}\n"
            f"👥 الكوبونات المتبقية: {course.get('coupons_left', '100')}\n"
            f"🌐 اللغة: 🇸🇦\n\n"
            f"⚡ سارع بالتسجيل قبل انتهاء العرض! ⚡"
        )

        # استخدام الرابط الأصلي مباشرة (تم إلغاء نظام الاختصار)
        shortened_link = course['link']

        # إنشاء أزرار
        keyboard = [
            [InlineKeyboardButton("🔥 الحصول على الكوبون 🔥", url=shortened_link)]
        ]

        if CHANNEL_USERNAME:
            keyboard.append([InlineKeyboardButton("🔔 اشترك في القناة", url=f"https://t.me/{CHANNEL_USERNAME}")])

        reply_markup = InlineKeyboardMarkup(keyboard)

        # إرسال للقناة
        try:
            # محاولة إرسال مع الصورة أولاً
            if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
                await bot.send_photo(
                    chat_id=CHANNEL_ID,
                    photo=course['thumbnail'],
                    caption=message_text,
                    reply_markup=reply_markup,
                    parse_mode=None
                )
            else:
                # إرسال نص فقط إذا لم تكن هناك صورة
                await bot.send_message(
                    chat_id=CHANNEL_ID,
                    text=message_text,
                    reply_markup=reply_markup,
                    disable_web_page_preview=True,
                    parse_mode=None
                )
        except Exception as photo_error:
            # إذا فشل إرسال الصورة، أرسل نص فقط
            logger.warning(f"فشل إرسال الصورة، إرسال نص فقط: {photo_error}")
            await bot.send_message(
                chat_id=CHANNEL_ID,
                text=message_text,
                reply_markup=reply_markup,
                disable_web_page_preview=True,
                parse_mode=None
            )

        logger.info(f"✅ تم إرسال الدورة للقناة: {course['title']}")

    except Exception as e:
        logger.error(f"خطأ في إرسال الدورة للقناة: {e}")

async def test_notification_system(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """اختبار نظام التنبيهات"""
    # التحقق من صلاحيات المشرف
    if update.effective_user.id not in ADMIN_IDS:
        if update.callback_query:
            await update.callback_query.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        else:
            await update.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        return

    # التعامل مع الرسائل من الأزرار أو الأوامر
    if update.callback_query:
        message = await update.callback_query.message.reply_text("🧪 جاري اختبار نظام التنبيهات...")
    else:
        message = await update.message.reply_text("🧪 جاري اختبار نظام التنبيهات...")

    try:
        # إنشاء دورة وهمية للاختبار (بدون صورة لتجنب مشاكل التحميل)
        test_course = {
            'title': '🧪 دورة اختبار النظام - Test Course',
            'link': 'https://www.udemy.com/course/test-course/?couponCode=TESTCODE123',
            'original_price': '$99.99',
            'coupons_left': 50,
            'thumbnail': '',  # بدون صورة للاختبار
            'language': 'ar'
        }

        await message.edit_text("🧪 إرسال تنبيه اختبار للمشرف...")

        # اختبار تنبيه المشرف
        admin_id = update.effective_user.id
        admin_message = (
            f"🧪 **اختبار نظام التنبيهات**\n\n"
            f"📝 {test_course['title']}\n"
            f"💰 السعر: {test_course['original_price']}\n"
            f"🔗 الرابط: {test_course['link']}\n"
            f"⏰ وقت الاختبار: {datetime.now().strftime('%H:%M:%S')}\n\n"
            f"✅ إذا وصلتك هذه الرسالة، فنظام التنبيهات يعمل بشكل صحيح!"
        )

        await context.bot.send_message(
            chat_id=admin_id,
            text=admin_message,
            parse_mode='Markdown',
            disable_web_page_preview=True
        )

        await message.edit_text("🧪 إرسال دورة اختبار للقناة...")

        # اختبار إرسال للقناة
        await send_course_to_channel(test_course, context.bot)

        await message.edit_text(
            "✅ **اختبار نظام التنبيهات مكتمل!**\n\n"
            "🔍 تحقق من:\n"
            "1️⃣ وصول تنبيه المشرف\n"
            "2️⃣ إرسال الدورة للقناة\n\n"
            "إذا وصل كلاهما، فالنظام يعمل بشكل مثالي! 🎉"
        )

    except Exception as e:
        logger.error(f"خطأ في اختبار نظام التنبيهات: {e}")
        await message.edit_text(f"❌ فشل اختبار النظام: {str(e)}")

def clean_expired_vip_job():
    """وظيفة تنظيف أعضاء VIP المنتهية الصلاحية"""
    logger.info("بدء تنظيف أعضاء VIP المنتهية الصلاحية...")
    try:
        # الحصول على جميع أعضاء VIP
        vip_users = vip_manager.get_all_vip_users()

        if not vip_users:
            logger.info("لا يوجد أعضاء VIP للتحقق منهم")
            return

        expired_users = []
        current_date = datetime.now().date()

        # التحقق من كل عضو VIP
        for user_id, user_info in vip_users.items():
            try:
                expiry_date = user_info.get("expires_at", "")
                if expiry_date:
                    expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                    if expiry < current_date:
                        expired_users.append((user_id, user_info.get("name", "غير معروف")))
            except Exception as e:
                logger.error(f"خطأ في التحقق من عضو VIP {user_id}: {e}")

        # حذف الأعضاء المنتهية الصلاحية
        removed_count = 0
        for user_id, name in expired_users:
            if vip_manager.remove_vip(int(user_id)):
                logger.info(f"تم حذف عضو VIP منتهي الصلاحية: {name} (ID: {user_id})")
                removed_count += 1
            else:
                logger.error(f"فشل في حذف عضو VIP: {name} (ID: {user_id})")

        if removed_count > 0:
            logger.info(f"تم حذف {removed_count} عضو VIP منتهي الصلاحية")
        else:
            logger.info("لا يوجد أعضاء VIP منتهية الصلاحية للحذف")

    except Exception as e:
        logger.error(f"خطأ في وظيفة تنظيف أعضاء VIP: {e}")

def verify_coupons_job():
    """وظيفة التحقق من صلاحية الكوبونات المخزنة"""
    logger.info("بدء التحقق من صلاحية الكوبونات المخزنة...")
    try:
        # قراءة الدورات المخزنة
        with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
            courses = json.load(f)

        if not courses:
            logger.info("لا توجد دورات مخزنة للتحقق منها")
            return

        # عدد الدورات قبل التحقق
        before_count = len(courses)
        logger.info(f"التحقق من {before_count} دورة مخزنة")

        # قائمة الدورات المحدثة
        updated_courses = []

        # التحقق من كل دورة
        for course in courses:
            try:
                # التحقق من رابط الدورة
                udemy_link = course.get('link')
                if not udemy_link:
                    continue

                # التحقق من حالة الكوبون
                coupon_status = check_course_coupon(course.get('comidoc_link', ''))

                # إذا كان الكوبون لا يزال صالحاً
                if coupon_status == "paid_with_coupon":
                    # التحقق من عدد الكوبونات المتبقية والسعر الأصلي
                    _, coupons_left, _, original_price = extract_udemy_link_and_coupons_left(course.get('comidoc_link', ''))

                    # تحديث عدد الكوبونات المتبقية والسعر الأصلي
                    course['coupons_left'] = coupons_left
                    course['original_price'] = original_price
                    course['last_verified'] = datetime.now(timezone.utc).isoformat()

                    # إضافة الدورة إلى القائمة المحدثة إذا كان لا يزال هناك كوبونات متبقية
                    if coupons_left > 0:
                        updated_courses.append(course)
                        logger.debug(f"الدورة {course['title']} لا تزال صالحة مع {coupons_left} كوبون متبقي")
                    else:
                        logger.info(f"تم استنفاد كوبونات الدورة: {course['title']}")
                else:
                    logger.info(f"انتهت صلاحية كوبون الدورة: {course['title']}")

            except Exception as e:
                logger.error(f"خطأ في التحقق من الدورة {course.get('title', '')}: {e}")
                # في حالة الخطأ، نحتفظ بالدورة كما هي
                updated_courses.append(course)

        # حفظ الدورات المحدثة
        with open(Config.COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(updated_courses, f, ensure_ascii=False, indent=2)

        # عدد الدورات بعد التحقق
        after_count = len(updated_courses)
        removed_count = before_count - after_count

        logger.info(f"اكتمل التحقق من الكوبونات. تمت إزالة {removed_count} دورة منتهية. تبقى {after_count} دورة صالحة.")

    except Exception as e:
        logger.error(f"خطأ في وظيفة التحقق من الكوبونات: {e}")

async def fetch_discudemy_courses():
    """جلب الدورات من موقع discudemy.com"""
    try:
        logger.info("بدء جلب الدورات من موقع discudemy.com...")

        # إنشاء كائن DiscudemyScraper
        scraper = DiscudemyScraper(
            base_url=Config.DISCUDEMY_URL,
            max_pages=Config.DISCUDEMY_MAX_PAGES,
            timeout=Config.REQUEST_TIMEOUT,
            concurrent_requests=Config.CONCURRENT_REQUESTS
        )

        # جلب الدورات
        courses = await scraper.fetch_all_courses()

        # حفظ الدورات في الملف
        if courses:
            with open(Config.DISCUDEMY_COURSES_FILE, 'w', encoding='utf-8') as f:
                json.dump(courses, f, ensure_ascii=False, indent=2)

            logger.info(f"تم حفظ {len(courses)} دورة من موقع discudemy.com")
        else:
            logger.warning("لم يتم العثور على دورات في موقع discudemy.com")

        return courses

    except Exception as e:
        logger.error(f"خطأ في جلب دورات discudemy.com: {e}")
        return []

async def get_discudemy_courses():
    """جلب الدورات من ملف discudemy المحفوظ"""
    try:
        # قراءة الدورات من الملف المحفوظ
        with open(Config.DISCUDEMY_COURSES_FILE, 'r', encoding='utf-8') as f:
            courses = json.load(f)

        # إضافة الحقول المفقودة للدورات من Discudemy
        processed_courses = []
        for course in courses:
            # إضافة الحقول المفقودة
            if 'coupons_left' not in course:
                course['coupons_left'] = 100  # قيمة افتراضية للكوبونات المتبقية
            if 'language' not in course:
                course['language'] = 'ar'  # افتراض أن الدورات عربية
            if 'status' not in course:
                course['status'] = 'paid_with_coupon'  # حالة الكوبون

            # التحقق من أن الدورة لديها رابط صالح
            if course.get('link') and 'couponCode=' in course.get('link', ''):
                processed_courses.append(course)

        logger.info(f"تم العثور على {len(processed_courses)} دورة من موقع discudemy.com")
        return processed_courses

    except FileNotFoundError:
        logger.warning(f"ملف دورات discudemy غير موجود. جاري جلب الدورات...")
        # إذا لم يكن الملف موجودًا، قم بجلب الدورات
        return await fetch_discudemy_courses()

    except Exception as e:
        logger.error(f"خطأ في جلب دورات discudemy: {e}")
        return []

async def fetch_real_discount_courses():
    """جلب الدورات من موقع real.discount"""
    try:
        logger.info("بدء جلب الدورات من موقع real.discount...")

        # إنشاء كائن RealDiscountScraper
        scraper = RealDiscountScraper()

        # جلب الدورات
        courses = scraper.run()

        # حفظ الدورات في الملف
        if courses:
            with open(Config.REAL_DISCOUNT_COURSES_FILE, 'w', encoding='utf-8') as f:
                json.dump(courses, f, ensure_ascii=False, indent=2)

            logger.info(f"تم حفظ {len(courses)} دورة من موقع real.discount")
        else:
            logger.warning("لم يتم العثور على دورات في موقع real.discount")

        return courses

    except Exception as e:
        logger.error(f"خطأ في جلب دورات real.discount: {e}")
        return []

async def get_real_discount_courses():
    """جلب الدورات من ملف real.discount المحفوظ"""
    try:
        # قراءة الدورات من الملف المحفوظ
        with open(Config.REAL_DISCOUNT_COURSES_FILE, 'r', encoding='utf-8') as f:
            courses = json.load(f)

        # إضافة الحقول المفقودة للدورات من Real Discount
        processed_courses = []
        for course in courses:
            # إضافة الحقول المفقودة
            if 'coupons_left' not in course:
                course['coupons_left'] = 100  # قيمة افتراضية للكوبونات المتبقية
            if 'language' not in course:
                course['language'] = 'ar'  # افتراض أن الدورات عربية
            if 'status' not in course:
                course['status'] = 'paid_with_coupon'  # حالة الكوبون

            # التحقق من أن الدورة لديها رابط صالح
            if course.get('link'):
                processed_courses.append(course)

        logger.info(f"تم العثور على {len(processed_courses)} دورة من موقع real.discount")
        return processed_courses

    except FileNotFoundError:
        logger.warning(f"ملف دورات real.discount غير موجود. جاري جلب الدورات...")
        # إذا لم يكن الملف موجودًا، قم بجلب الدورات
        return await fetch_real_discount_courses()

    except Exception as e:
        logger.error(f"خطأ في جلب دورات real.discount: {e}")
        return []

def update_discudemy_courses_job():
    """وظيفة تحديث دورات discudemy للمجدول"""
    run_async_task(fetch_discudemy_courses())

def auto_check_new_courses_job():
    """وظيفة الفحص التلقائي للمجدول"""
    try:
        logger.info("🔄 بدء تشغيل مهمة الفحص التلقائي...")
        run_async_task(auto_check_new_courses())
        logger.info("✅ انتهت مهمة الفحص التلقائي")
    except Exception as e:
        logger.error(f"❌ خطأ في مهمة الفحص التلقائي: {e}")

def schedule_updates():
    """جدولة تحديث الدورات والتحقق من الكوبونات"""
    # جدولة تحديث الدورات
    scheduler.add_job(
        func=update_ar_courses_job,
        trigger='interval',
        minutes=15,
        id='update_ar_courses',
        replace_existing=True
    )

    # جدولة تحديث دورات discudemy
    scheduler.add_job(
        func=update_discudemy_courses_job,
        trigger='interval',
        minutes=30,
        id='update_discudemy_courses',
        replace_existing=True
    )

    # جدولة التحقق من صلاحية الكوبونات كل ساعتين
    scheduler.add_job(
        func=verify_coupons_job,
        trigger='interval',
        hours=2,
        id='verify_coupons',
        replace_existing=True
    )

    # جدولة تنظيف أعضاء VIP المنتهية الصلاحية يومياً في الساعة 2:00 صباحاً
    scheduler.add_job(
        func=clean_expired_vip_job,
        trigger='cron',
        hour=2,
        minute=0,
        id='clean_expired_vip',
        replace_existing=True
    )

    # جدولة تنظيف بيانات حدود التحديث القديمة أسبوعياً
    scheduler.add_job(
        func=clean_old_update_limits,
        trigger='cron',
        day_of_week=0,  # الأحد
        hour=3,
        minute=0,
        id='clean_update_limits',
        replace_existing=True
    )

    # جدولة الفحص التلقائي للدورات الجديدة كل 10 دقائق
    logger.info("📅 إضافة مهمة الفحص التلقائي للمجدول...")
    scheduler.add_job(
        func=auto_check_new_courses_job,
        trigger='interval',
        minutes=10,
        id='auto_check_courses',
        replace_existing=True
    )
    logger.info("✅ تم إضافة مهمة الفحص التلقائي بنجاح")

    scheduler.start()
    logger.info("🚀 تم تشغيل الجدولة التلقائية - فحص الدورات كل 10 دقائق")

    # عرض جميع المهام المجدولة
    jobs = scheduler.get_jobs()
    logger.info(f"📋 المهام المجدولة: {len(jobs)}")
    for job in jobs:
        logger.info(f"  - {job.id}: {job.next_run_time}")

async def get_courses_by_language(language='ar'):
    """جلب الدورات حسب اللغة المحددة من الملف المحفوظ"""
    try:
        # قراءة الدورات من الملف المحفوظ
        with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
            all_courses = json.load(f)

        # تصفية الدورات حسب اللغة
        filtered_courses = [
            course for course in all_courses
            if course.get('language') == language and course.get('coupons_left', 0) > 0
        ]

        logger.info(f"تم العثور على {len(filtered_courses)} دورة بلغة {language}")
        return filtered_courses

    except FileNotFoundError:
        logger.warning(f"ملف الدورات غير موجود. جاري جلب الدورات...")
        # إذا لم يكن الملف موجودًا، قم بجلب الدورات
        return await fetch_all_courses(language)

    except Exception as e:
        logger.error(f"خطأ في جلب الدورات: {e}")
        return []

# تعديل الأوامر للتعامل مع الدورات حسب اللغة
async def arabic_courses_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """أمر لعرض الدورات العربية"""
    # التحقق من صلاحية المستخدم
    if not await check_user_authorization(update):
        return

    courses = await get_courses_by_language('ar')
    if courses:
        # عكس ترتيب الدورات من الأسفل إلى الأعلى
        courses = list(reversed(courses))

        for course in courses:
            await send_course_message(update, course)
    else:
        await update.message.reply_text("❌ لم يتم العثور على دورات عربية متاحة")



async def error_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالجة الأخطاء"""
    logger.error(f"Exception while handling an update: {context.error}")
    try:
        # إضافة معلومات عن التحديث الذي تسبب في الخطأ
        error_message = f"⚠️ حدث خطأ في البوت:\n{context.error}"

        if update:
            user_info = f"المستخدم: {update.effective_user.id if update.effective_user else 'غير معروف'}"
            chat_info = f"المحادثة: {update.effective_chat.id if update.effective_chat else 'غير معروفة'}"
            error_message += f"\n{user_info}\n{chat_info}"

        # إرسال رسالة للمشرفين
        for admin_id in ADMIN_IDS:
            await context.bot.send_message(
                chat_id=admin_id,
                text=error_message
            )
    except Exception as e:
        logger.error(f"خطأ في إرسال إشعار الخطأ للمشرفين: {e}")

async def handle_vip_subscribe(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالجة طلب الاشتراك في خدمة VIP"""
    query = update.callback_query
    user_id = update.effective_user.id

    # التحقق مما إذا كان المستخدم VIP بالفعل
    if vip_manager.is_vip(user_id):
        await query.message.reply_text(
            "✅ أنت بالفعل مشترك في خدمة VIP!\n"
            "يمكنك الاستمتاع بالروابط المباشرة بدون اختصار.\n"
            "استخدم الأمر /vip_info لعرض تفاصيل اشتراكك."
        )
        return



    # إنشاء رسالة الاشتراك بتنسيق جذاب ومحسن
    message = (
        "🎓 <b>استفد من أقوى عروض الكورسات التعليمية الآن!</b>\n\n"
        "✅ روابط مباشرة بدون إعلانات أو اختصارات\n"
        "✅ وصول فوري لأحدث الكورسات\n"
        "✅ دعم فني مميز وسريع\n"
        "✅ وفر وقتك وجهدك في البحث!\n\n"
        "💸 <b>العرض الحالي:</b>\n"
        "📆 اشتراك لمدة شهر واحد فقط بـ 1 دولار!\n"
        "⚠️ العرض محدود — سارع بالاشتراك الآن!\n\n"
        "📲 للاشتراك، تواصل مع المسؤول:\n"
        "👤 @GurusVIP"
    )

    # إنشاء أزرار
    keyboard = [
        [InlineKeyboardButton("💬 تواصل مع المسؤول", url="https://t.me/GurusVIP")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await query.message.reply_text(
        message,
        reply_markup=reply_markup,
        parse_mode='HTML'
    )

async def handle_vip_info(update: Update, _context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالجة طلب عرض معلومات VIP من خلال الأزرار"""
    query = update.callback_query
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name

    # التحقق مما إذا كان المستخدم VIP
    if vip_manager.is_vip(user_id):
        # الحصول على معلومات VIP
        vip_info = vip_manager.get_vip_info(user_id)

        if vip_info:
            joined_date = vip_info.get("joined_at", "غير محدد")
            expiry_date = vip_info.get("expires_at", "غير محدد")
            days_total = vip_info.get("days_total", 0)

            # حساب الأيام المتبقية
            try:
                expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                days_left = (expiry - datetime.now().date()).days
                days_left_text = f"{days_left} يوم" if days_left > 0 else "منتهي"
            except:
                days_left_text = "غير محدد"

            message = (
                "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                f"👤 المستخدم: {user_name}\n"
                f"📅 تاريخ الاشتراك: {joined_date}\n"
                f"⏳ تاريخ الانتهاء: {expiry_date}\n"
                f"⌛ المدة المتبقية: {days_left_text}\n"
                f"📊 مدة الاشتراك: {days_total} يوم\n\n"
                "✅ أنت تتمتع بجميع مميزات VIP!\n"
                "• روابط مباشرة بدون اختصار\n"
                "• تجاوز صفحات الإعلانات\n"
                "• دعم فني مميز"
            )
        else:
            # في حالة كان المستخدم مشرفًا (VIP تلقائي)
            message = (
                "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                f"👤 المستخدم: {user_name}\n"
                "🔑 الحالة: مشرف (VIP تلقائي)\n\n"
                "✅ أنت تتمتع بجميع مميزات VIP!\n"
                "• روابط مباشرة بدون اختصار\n"
                "• تجاوز صفحات الإعلانات\n"
                "• دعم فني مميز"
            )
    else:


        # إنشاء رسالة بتنسيق جذاب ومحسن
        message = (
            "❗ <b>أنت غير مشترك في خدمة VIP</b> ❗\n\n"
            "🎓 <b>استفد من أقوى عروض الكورسات التعليمية الآن!</b>\n\n"
            "✅ روابط مباشرة بدون إعلانات أو اختصارات\n"
            "✅ وصول فوري لأحدث الكورسات\n"
            "✅ دعم فني مميز وسريع\n"
            "✅ وفر وقتك وجهدك في البحث!\n\n"
            "💸 <b>العرض الحالي:</b>\n"
            "📆 اشتراك لمدة شهر واحد فقط بـ 1 دولار!\n"
            "🎯 استثمر في نفسك بأقل من ثمن كوب شاي يوميًا\n"
            "⚠️ العرض محدود — سارع بالاشتراك الآن!\n\n"
            "📲 للاشتراك، تواصل مع المسؤول:\n"
            "👤 @GurusVIP"
        )

    # إنشاء أزرار
    keyboard = [
        [InlineKeyboardButton("💬 تواصل مع المسؤول", url="https://t.me/GurusVIP")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await query.message.reply_text(
        message,
        reply_markup=reply_markup,
        parse_mode='HTML'
    )

async def vip_info_command(update: Update, _context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض معلومات VIP للمستخدم"""
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name

    # التحقق مما إذا كان المستخدم VIP
    if vip_manager.is_vip(user_id):
        # الحصول على معلومات VIP
        vip_info = vip_manager.get_vip_info(user_id)

        if vip_info:
            joined_date = vip_info.get("joined_at", "غير محدد")
            expiry_date = vip_info.get("expires_at", "غير محدد")
            days_total = vip_info.get("days_total", 0)

            # حساب الأيام المتبقية
            try:
                expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                days_left = (expiry - datetime.now().date()).days
                days_left_text = f"{days_left} يوم" if days_left > 0 else "منتهي"
            except:
                days_left_text = "غير محدد"

            message = (
                "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                f"👤 المستخدم: {user_name}\n"
                f"📅 تاريخ الاشتراك: {joined_date}\n"
                f"⏳ تاريخ الانتهاء: {expiry_date}\n"
                f"⌛ المدة المتبقية: {days_left_text}\n"
                f"📊 مدة الاشتراك: {days_total} يوم\n\n"
                "✅ أنت تتمتع بجميع مميزات VIP!\n"
                "• روابط مباشرة بدون اختصار\n"
                "• تجاوز صفحات الإعلانات\n"
                "• دعم فني مميز"
            )
        else:
            # في حالة كان المستخدم مشرفًا (VIP تلقائي)
            message = (
                "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                f"👤 المستخدم: {user_name}\n"
                "🔑 الحالة: مشرف (VIP تلقائي)\n\n"
                "✅ أنت تتمتع بجميع مميزات VIP!\n"
                "• روابط مباشرة بدون اختصار\n"
                "• تجاوز صفحات الإعلانات\n"
                "• دعم فني مميز"
            )
    else:
        # رسالة للمستخدمين غير VIP محسنة
        message = (
            "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
            "❌ أنت غير مشترك في خدمة VIP.\n\n"
            "🎓 <b>استفد من أقوى عروض الكورسات التعليمية الآن!</b>\n\n"
            "✅ روابط مباشرة بدون إعلانات أو اختصارات\n"
            "✅ وصول فوري لأحدث الكورسات\n"
            "✅ دعم فني مميز وسريع\n"
            "✅ وفر وقتك وجهدك في البحث!\n\n"
            "💸 <b>العرض الحالي:</b>\n"
            "📆 اشتراك لمدة شهر واحد فقط بـ 1 دولار!\n"
            "🎯 استثمر في نفسك بأقل من ثمن كوب شاي يوميًا\n"
            "⚠️ العرض محدود — سارع بالاشتراك الآن!\n\n"
            "📩 للاشتراك والتواصل:\n"
            "👤 @GurusVIP"
        )

    # إنشاء أزرار
    keyboard = [
        [InlineKeyboardButton("💬 تواصل مع المسؤول", url="https://t.me/GurusVIP")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await update.message.reply_text(
        message,
        reply_markup=reply_markup,
        parse_mode='HTML'
    )

async def add_vip_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """إضافة مستخدم VIP (للمشرفين فقط)"""
    # التحقق من صلاحيات المشرف
    if not await admin_only(update, context):
        return

    # التحقق من وجود المعلمات المطلوبة
    if not context.args or len(context.args) < 2:
        await update.message.reply_text(
            "❌ يرجى تحديد معرف المستخدم وعدد الأيام\n\n"
            "الاستخدام الصحيح:\n"
            "/add_vip 123456789 30"
        )
        return

    try:
        # استخراج المعلمات
        user_id = int(context.args[0])
        days = int(context.args[1])
        name = " ".join(context.args[2:]) if len(context.args) > 2 else f"VIP User {user_id}"

        # إضافة المستخدم
        if vip_manager.add_vip(user_id, name, days):
            expiry_date = (datetime.now() + timedelta(days=days)).strftime("%Y-%m-%d")
            await update.message.reply_text(
                f"✅ تم إضافة عضو VIP جديد بنجاح!\n\n"
                f"👤 الاسم: {name}\n"
                f"🆔 المعرف: {user_id}\n"
                f"⏳ المدة: {days} يوم\n"
                f"📅 تاريخ الانتهاء: {expiry_date}"
            )
        else:
            await update.message.reply_text("❌ حدث خطأ أثناء إضافة العضو")

    except ValueError:
        await update.message.reply_text("❌ تأكد من صحة المعرف وعدد الأيام")
    except Exception as e:
        await update.message.reply_text(f"❌ حدث خطأ غير متوقع: {str(e)}")

async def remove_vip_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """حذف مستخدم VIP (للمشرفين فقط)"""
    # التحقق من صلاحيات المشرف
    if not await admin_only(update, context):
        return

    # التحقق من وجود المعلمات المطلوبة
    if not context.args or len(context.args) < 1:
        await update.message.reply_text(
            "❌ يرجى تحديد معرف المستخدم المراد حذفه\n\n"
            "الاستخدام الصحيح:\n"
            "/remove_vip 123456789"
        )
        return

    try:
        # استخراج المعلمات
        user_id = int(context.args[0])

        # الحصول على معلومات المستخدم قبل الحذف
        vip_info = vip_manager.get_vip_info(user_id)

        if not vip_info:
            await update.message.reply_text(
                f"❌ المستخدم غير موجود في قائمة VIP\n"
                f"المعرف: {user_id}"
            )
            return

        user_name = vip_info.get("name", "غير معروف")

        # حذف المستخدم
        if vip_manager.remove_vip(user_id):
            await update.message.reply_text(
                f"✅ تم حذف عضو VIP بنجاح!\n\n"
                f"👤 الاسم: {user_name}\n"
                f"🆔 المعرف: {user_id}\n"
                f"🗑️ تم الحذف في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
        else:
            await update.message.reply_text("❌ حدث خطأ أثناء حذف العضو")

    except ValueError:
        await update.message.reply_text("❌ تأكد من صحة معرف المستخدم")
    except Exception as e:
        await update.message.reply_text(f"❌ حدث خطأ غير متوقع: {str(e)}")

async def extend_vip_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تمديد اشتراك VIP (للمشرفين فقط)"""
    # التحقق من صلاحيات المشرف
    if not await admin_only(update, context):
        return

    # التحقق من وجود المعلمات المطلوبة
    if not context.args or len(context.args) < 2:
        await update.message.reply_text(
            "❌ يرجى تحديد معرف المستخدم وعدد الأيام للتمديد\n\n"
            "الاستخدام الصحيح:\n"
            "/extend_vip 123456789 30"
        )
        return

    try:
        # استخراج المعلمات
        user_id = int(context.args[0])
        additional_days = int(context.args[1])

        if additional_days <= 0:
            await update.message.reply_text("❌ عدد الأيام يجب أن يكون أكبر من صفر")
            return

        # الحصول على معلومات المستخدم الحالية
        vip_info = vip_manager.get_vip_info(user_id)

        if not vip_info:
            await update.message.reply_text(
                f"❌ المستخدم غير موجود في قائمة VIP\n"
                f"المعرف: {user_id}\n\n"
                f"استخدم /add_vip لإضافة عضو جديد"
            )
            return

        user_name = vip_info.get("name", "غير معروف")
        current_expiry = vip_info.get("expires_at", "")

        # حساب تاريخ الانتهاء الجديد
        try:
            current_expiry_date = datetime.strptime(current_expiry, "%Y-%m-%d").date()
            # إذا كان الاشتراك منتهي، نبدأ من اليوم الحالي
            if current_expiry_date < datetime.now().date():
                new_expiry_date = datetime.now().date() + timedelta(days=additional_days)
            else:
                # إذا كان الاشتراك لا يزال نشطاً، نضيف الأيام للتاريخ الحالي
                new_expiry_date = current_expiry_date + timedelta(days=additional_days)
        except:
            # في حالة خطأ في التاريخ، نبدأ من اليوم الحالي
            new_expiry_date = datetime.now().date() + timedelta(days=additional_days)

        # تحديث بيانات العضو
        vip_info["expires_at"] = str(new_expiry_date)
        vip_info["days_total"] = vip_info.get("days_total", 0) + additional_days

        # حفظ البيانات المحدثة
        vip_manager.vip_data[str(user_id)] = vip_info
        if vip_manager._save_vip_data():
            await update.message.reply_text(
                f"✅ تم تمديد اشتراك VIP بنجاح!\n\n"
                f"👤 الاسم: {user_name}\n"
                f"🆔 المعرف: {user_id}\n"
                f"📅 التاريخ السابق: {current_expiry}\n"
                f"📅 التاريخ الجديد: {new_expiry_date}\n"
                f"➕ الأيام المضافة: {additional_days} يوم\n"
                f"🕰️ تم التمديد في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
        else:
            await update.message.reply_text("❌ حدث خطأ أثناء حفظ البيانات")

    except ValueError:
        await update.message.reply_text("❌ تأكد من صحة معرف المستخدم وعدد الأيام")
    except Exception as e:
        await update.message.reply_text(f"❌ حدث خطأ غير متوقع: {str(e)}")

async def toggle_notifications_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """تفعيل/إيقاف إشعارات الفحص التلقائي"""
    if not await admin_only(update, context):
        return

    global AUTO_CHECK_NOTIFICATIONS
    AUTO_CHECK_NOTIFICATIONS = not AUTO_CHECK_NOTIFICATIONS

    status = "مفعلة ✅" if AUTO_CHECK_NOTIFICATIONS else "معطلة ❌"
    await update.message.reply_text(
        f"🔔 **إشعارات الفحص التلقائي**\n\n"
        f"الحالة: {status}\n\n"
        f"ℹ️ الإشعارات تشمل:\n"
        f"• بدء الفحص كل 10 دقائق\n"
        f"• نتائج الفحص (دورات جديدة أو لا)\n"
        f"• أخطاء الفحص إن وجدت\n\n"
        f"💡 يمكنك تغيير الحالة بإعادة استخدام الأمر"
    )

async def toggle_notifications_button(update: Update, _context: ContextTypes.DEFAULT_TYPE) -> None:
    """تفعيل/إيقاف إشعارات الفحص التلقائي من الزر"""
    query = update.callback_query

    # التحقق من صلاحيات المشرف
    if query.from_user.id not in ADMIN_IDS:
        await query.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        return

    global AUTO_CHECK_NOTIFICATIONS
    AUTO_CHECK_NOTIFICATIONS = not AUTO_CHECK_NOTIFICATIONS

    status = "مفعلة ✅" if AUTO_CHECK_NOTIFICATIONS else "معطلة ❌"

    # إرسال رسالة تأكيد
    await query.message.reply_text(
        f"🔔 **تم تغيير إعدادات الإشعارات**\n\n"
        f"الحالة الجديدة: {status}\n\n"
        f"ℹ️ الإشعارات تشمل:\n"
        f"• بدء الفحص كل 10 دقائق\n"
        f"• نتائج الفحص (دورات جديدة أو لا)\n"
        f"• أخطاء الفحص إن وجدت"
    )

    # تحديث لوحة التحكم بالأزرار الجديدة
    await show_admin_panel_updated(query)

async def test_auto_check_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """اختبار الفحص التلقائي يدوياً"""
    # التحقق من صلاحيات المشرف
    if update.effective_user.id not in ADMIN_IDS:
        await update.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        return

    message = await update.message.reply_text("🧪 **جاري اختبار الفحص التلقائي...**\n\n🔍 فحص 5 صفحات من Comidoc...")

    try:
        import time
        start_time = time.time()

        # قراءة الدورات الحالية
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                current_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            current_courses = []

        current_links = {course.get('link') for course in current_courses}
        initial_count = len(current_courses)

        await message.edit_text("🧪 **جاري اختبار الفحص التلقائي...**\n\n📊 قراءة الدورات الحالية: " + str(initial_count) + " دورة")

        # فحص 5 صفحات
        new_courses = []
        for page in range(1, 6):  # صفحات 1-5
            await message.edit_text(f"🧪 **جاري اختبار الفحص التلقائي...**\n\n🌐 فحص الصفحة {page}/5...")
            page_courses = await process_page(page, 'ar')
            new_courses.extend(page_courses)

        total_found = len(new_courses)

        # تصفية الدورات الجديدة فقط
        truly_new_courses = []
        for course in new_courses:
            if course.get('link') not in current_links:
                # التحقق من كون الدورة محظورة
                if not is_course_blocked(course.get('link', ''), course.get('title', '')):
                    truly_new_courses.append(course)

        new_count = len(truly_new_courses)
        end_time = time.time()
        duration = round(end_time - start_time, 1)

        # إعداد التقرير
        if truly_new_courses:
            # إضافة الدورات الجديدة للملف
            all_courses = current_courses + truly_new_courses
            with open(Config.COURSES_FILE, 'w', encoding='utf-8') as f:
                json.dump(all_courses, f, ensure_ascii=False, indent=2)

            # إرسال للقناة
            await message.edit_text(f"🧪 **اختبار الفحص التلقائي**\n\n📊 إرسال {new_count} دورة جديدة للقناة...")

            for course in truly_new_courses[:3]:  # أول 3 دورات فقط
                try:
                    await send_course_to_channel(course, context.bot)
                    await asyncio.sleep(2)  # انتظار بين الرسائل
                except Exception as e:
                    logger.error(f"خطأ في إرسال دورة للقناة: {e}")

            # التقرير النهائي
            report = (
                f"✅ **اختبار الفحص التلقائي مكتمل!**\n\n"
                f"🔍 تم فحص: 5 صفحات\n"
                f"📊 دورات موجودة مسبقاً: {initial_count}\n"
                f"📈 إجمالي الدورات المجمعة: {total_found}\n"
                f"🆕 دورات جديدة: {new_count}\n"
                f"📢 تم إرسال {min(new_count, 3)} دورة للقناة\n"
                f"⏱️ وقت الفحص: {duration} ثانية\n\n"
                f"🎉 النظام يعمل بشكل مثالي!"
            )
        else:
            report = (
                f"✅ **اختبار الفحص التلقائي مكتمل!**\n\n"
                f"🔍 تم فحص: 5 صفحات\n"
                f"📊 دورات موجودة مسبقاً: {initial_count}\n"
                f"📈 إجمالي الدورات المجمعة: {total_found}\n"
                f"🆕 دورات جديدة: 0\n"
                f"ℹ️ لا توجد دورات جديدة\n"
                f"⏱️ وقت الفحص: {duration} ثانية\n\n"
                f"✅ النظام يعمل بشكل صحيح!"
            )

        await message.edit_text(report, parse_mode='Markdown')

    except Exception as e:
        logger.error(f"خطأ في اختبار الفحص التلقائي: {e}")
        await message.edit_text(f"❌ **فشل اختبار الفحص التلقائي**\n\nالخطأ: {str(e)}", parse_mode='Markdown')

async def test_auto_check_button(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """اختبار الفحص التلقائي من الزر"""
    query = update.callback_query

    # التحقق من صلاحيات المشرف
    if query.from_user.id not in ADMIN_IDS:
        await query.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        return

    # تشغيل نفس دالة الاختبار
    # إنشاء update وهمي للأمر النصي
    class FakeUpdate:
        def __init__(self, user_id):
            self.effective_user = type('obj', (object,), {'id': user_id})
            self.message = query.message

    fake_update = FakeUpdate(query.from_user.id)
    await test_auto_check_command(fake_update, context)

async def show_admin_panel_updated(query):
    """تحديث لوحة المشرف بالأزرار المحدثة"""
    try:
        # إنشاء أزرار متجاورة للوحة المشرف
        row1 = [
            InlineKeyboardButton("👥 إدارة VIP", callback_data='manage_vip'),
            InlineKeyboardButton("📊 إحصائيات", callback_data='stats')
        ]

        row2 = [
            InlineKeyboardButton("🇸🇦 تحديث عربية", callback_data='update_comidoc_courses'),
            InlineKeyboardButton("🇺🇸 تحديث إنجليزية", callback_data='update_en_courses')
        ]

        row3 = [
            InlineKeyboardButton("💰 تحديث Discudemy", callback_data='update_discudemy_courses'),
            InlineKeyboardButton("🔥 تحديث Real Discount", callback_data='update_real_discount_courses')
        ]

        row4_extra = [
            InlineKeyboardButton("🔄 تحديث الكل", callback_data='update_all_courses')
        ]

        row5 = [
            InlineKeyboardButton("🧹 تنظيف الكوبونات", callback_data='clean_expired_coupons')
        ]

        row6 = [
            InlineKeyboardButton("🧹 تنظيف VIP المنتهية", callback_data='clean_expired_vip'),
            InlineKeyboardButton("🧪 اختبار الفحص التلقائي", callback_data='test_auto_check')
        ]

        # زر تفعيل/إيقاف الإشعارات (محدث)
        notification_status = "🔔 إيقاف الإشعارات" if AUTO_CHECK_NOTIFICATIONS else "🔕 تفعيل الإشعارات"
        row7 = [
            InlineKeyboardButton(notification_status, callback_data='toggle_notifications')
        ]

        keyboard = [row1, row2, row3, row4_extra, row5, row6, row7]
        reply_markup = InlineKeyboardMarkup(keyboard)

        # تحديث الأزرار في الرسالة الحالية
        await query.edit_message_reply_markup(reply_markup=reply_markup)

    except Exception as e:
        logger.error(f"خطأ في تحديث لوحة التحكم: {e}")

async def send_to_channel_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """أمر إرسال الدورات إلى القناة (للمشرفين فقط)"""
    # التحقق من صلاحيات المشرف
    if not await admin_only(update, context):
        return

    message = await update.message.reply_text("جاري إرسال الدورات إلى القناة...")

    try:
        # قراءة الدورات من الملف
        with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
            all_courses = json.load(f)

        # قراءة الدورات المرسلة سابقًا
        try:
            with open(Config.SENT_COURSES_FILE, 'r', encoding='utf-8') as f:
                sent_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            sent_courses = []

        # الحصول على روابط الدورات المرسلة سابقًا
        sent_links = [course.get('link') for course in sent_courses]

        # تصفية الدورات التي لم يتم إرسالها بعد ولديها كوبونات متبقية
        new_courses = [
            course for course in all_courses
            if course.get('link') not in sent_links
            and course.get('coupons_left', 0) > 0
            and course.get('language') == 'ar'  # فقط الدورات العربية
        ]

        if not new_courses:
            await message.edit_text("❌ لا توجد دورات جديدة للنشر")
            return

        # نشر الدورات الجديدة (بحد أقصى 5 دورات)
        courses_to_publish = new_courses[:5]
        published_count = 0

        for course in courses_to_publish:
            try:
                # تحضير نص الرسالة بالتنسيق الاحترافي
                language_flag = "🇸🇦" if course.get('language') == 'ar' else "🇺🇸"

                # إضافة إيموجي عشوائي للعنوان
                title_emojis = ["✨", "🔥", "⭐", "🌟", "💫", "🎯", "🚀", "📚", "🎓", "💻", "📱", "🖥️", "📊"]
                random_emoji1 = title_emojis[hash(course['title']) % len(title_emojis)]
                random_emoji2 = title_emojis[(hash(course['title']) + 3) % len(title_emojis)]

                # تحديد سعر الدورة الأصلي من البيانات المستخرجة
                original_price = course.get('original_price', "49.99$")

                # تحديد تاريخ النشر (التاريخ الحالي)
                current_date = datetime.now().strftime("%Y-%m-%d")

                # استخدام الرابط الأصلي مباشرة (تم إلغاء نظام الاختصار)
                course_link = course['link']

                message_text = (
                    f"{random_emoji1} كورس جديد متاح مجاناً! {random_emoji2}\n\n"
                    f"📝 {course['title']}\n\n"
                    f"💰 السعر الأصلي: {original_price}\n"
                    f"🎁 السعر الآن: مجاني (خصم 100%)\n"
                    f"🔑 كوبون: مفعل تلقائي\n"
                    f"🏢 المنصة: Udemy\n"
                    f"📅 تاريخ النشر: {current_date}\n"
                    f"👥 الكوبونات المتبقية: {course.get('coupons_left', '100')}\n"
                    f"🌐 اللغة: {language_flag}\n\n"
                    f"💎 للحصول على روابط مباشرة بدون اختصار، اشترك في خدمة VIP!\n\n"
                    f"⚡ سارع بالتسجيل قبل انتهاء العرض! ⚡"
                )

                # إنشاء أزرار
                keyboard = [
                    [InlineKeyboardButton("🔥 الحصول على الكوبون 🔥", url=course_link)]
                ]

                if CHANNEL_USERNAME:
                    keyboard.append([InlineKeyboardButton("🔔 اشترك في القناة", url=f"https://t.me/{CHANNEL_USERNAME}")])

                # إضافة زر للاشتراك في خدمة VIP
                keyboard.append([InlineKeyboardButton("💎 اشترك في خدمة VIP", url="https://t.me/GurusVIP")])

                reply_markup = InlineKeyboardMarkup(keyboard)

                # استخدام معرف القناة الصحيح
                channel_id = -1002613463650  # معرف القناة الصحيح

                # إرسال الرسالة إلى القناة مع الصورة إذا كانت متوفرة
                if course.get('thumbnail') and course['thumbnail'] != 'https://via.placeholder.com/150':
                    # إرسال الرسالة مع الصورة
                    await context.bot.send_photo(
                        chat_id=channel_id,
                        photo=course['thumbnail'],
                        caption=message_text,
                        reply_markup=reply_markup,
                        parse_mode=None  # عدم استخدام أي تنسيق لتجنب الأخطاء
                    )
                else:
                    # إرسال الرسالة بدون صورة
                    await context.bot.send_message(
                        chat_id=channel_id,
                        text=message_text,
                        reply_markup=reply_markup,
                        disable_web_page_preview=True,
                        parse_mode=None  # عدم استخدام أي تنسيق لتجنب الأخطاء
                    )

                # إضافة الدورة إلى قائمة الدورات المرسلة
                course['sent_date'] = datetime.now(timezone.utc).isoformat()
                sent_courses.append(course)
                published_count += 1

                # انتظار قليلاً لتجنب تجاوز حدود التليجرام
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"خطأ في نشر الدورة {course.get('title')}: {e}")

        # حفظ الدورات المرسلة
        with open(Config.SENT_COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(sent_courses, f, ensure_ascii=False, indent=2)

        await message.edit_text(f"✅ تم نشر {published_count} دورة في القناة بنجاح")

    except Exception as e:
        logger.error(f"خطأ في نشر الدورات: {e}")
        await message.edit_text("❌ حدث خطأ في نشر الدورات")

async def clean_courses_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """أمر تنظيف الكوبونات المنتهية (للمشرفين فقط)"""
    # التحقق من صلاحيات المشرف
    if not await admin_only(update, context):
        return

    message = await update.message.reply_text("جاري تنظيف الكوبونات المنتهية...")

    try:
        # قراءة الدورات من الملف
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                all_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            all_courses = []

        # عدد الدورات قبل التنظيف
        before_count = len(all_courses)

        # تصفية الدورات التي لديها كوبونات متبقية فقط
        active_courses = [course for course in all_courses if course.get('coupons_left', 0) > 0]

        # عدد الدورات بعد التنظيف
        after_count = len(active_courses)
        removed_count = before_count - after_count

        # حفظ الدورات النشطة
        with open(Config.COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(active_courses, f, ensure_ascii=False, indent=2)

        await message.edit_text(
            f"✅ تم تنظيف الكوبونات المنتهية بنجاح\n"
            f"تم إزالة {removed_count} دورة منتهية\n"
            f"الدورات النشطة المتبقية: {after_count}"
        )

    except Exception as e:
        logger.error(f"خطأ في تنظيف الكوبونات المنتهية: {e}")
        await message.edit_text("❌ حدث خطأ في تنظيف الكوبونات المنتهية")

async def courses_command(update: Update, _context: ContextTypes.DEFAULT_TYPE) -> None:
    """أمر عرض الدورات المجانية"""
    await update.message.reply_text("جاري تحميل الدورات المجانية...")

    # استخدام نفس وظيفة عرض الدورات العربية
    courses = await get_courses_by_language('ar')
    if courses:
        # عكس ترتيب الدورات من الأسفل إلى الأعلى
        courses = list(reversed(courses))

        for course in courses:
            await send_course_message(update, course)
    else:
        await update.message.reply_text("❌ لم يتم العثور على دورات مجانية متاحة")

async def check_new_command(update: Update, _context: ContextTypes.DEFAULT_TYPE) -> None:
    """أمر التحقق من وجود دورات جديدة"""
    message = await update.message.reply_text("جاري التحقق من وجود دورات جديدة...")

    try:
        # قراءة الدورات الحالية
        try:
            with open(Config.COURSES_FILE, 'r', encoding='utf-8') as f:
                old_courses = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            old_courses = []

        old_links = [course.get('link') for course in old_courses]

        # جلب الدورات الجديدة
        new_courses = await fetch_all_courses('ar')
        new_count = len(new_courses)

        # حساب عدد الدورات الجديدة
        actually_new_courses = [course for course in new_courses if course.get('link') not in old_links]
        new_added_count = len(actually_new_courses)

        # عرض النتائج
        if new_added_count > 0:
            await message.edit_text(
                f"✅ تم العثور على {new_added_count} دورة جديدة!\n"
                f"إجمالي الدورات الآن: {new_count}\n"
                f"سيتم عرض أحدث الدورات الجديدة:"
            )

            # عرض الدورات الجديدة (بحد أقصى 5)
            for course in actually_new_courses[:5]:
                await send_course_message(update, course)
        else:
            await message.edit_text(
                f"ℹ️ لم يتم العثور على دورات جديدة.\n"
                f"إجمالي الدورات الحالية: {new_count}"
            )

    except Exception as e:
        logger.error(f"خطأ في التحقق من الدورات الجديدة: {e}")
        await message.edit_text("❌ حدث خطأ أثناء التحقق من الدورات الجديدة")

async def cooldown_status_command(update: Update, _context: ContextTypes.DEFAULT_TYPE) -> None:
    """أمر عرض حالة نظام التحكم في التوقيت (للمشرفين فقط)"""
    if not is_admin(update.effective_user.id):
        # التحقق من نوع التحديث (زر أم أمر نصي)
        if update.callback_query:
            await update.callback_query.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        else:
            await update.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
        return

    try:
        can_update, remaining_seconds = update_cooldown_manager.can_update()
        last_update_info = update_cooldown_manager.get_last_update_info()

        status_message = "⏰ **حالة نظام التحكم في التوقيت الصارم**\n\n"
        status_message += f"🕐 فترة الانتظار: {update_cooldown_manager.cooldown_minutes} دقائق بين أي تحديث\n"
        status_message += f"📊 آخر تحديث: {last_update_info}\n\n"

        if can_update:
            status_message += "✅ **حالة النظام:** جميع التحديثات متاحة الآن\n"
            status_message += "🟢 يمكن تحديث أي نوع من الدورات\n"
        else:
            remaining_time = update_cooldown_manager.get_remaining_time_text(remaining_seconds)
            status_message += f"⏳ **حالة النظام:** جميع التحديثات محظورة\n"
            status_message += f"🔴 الوقت المتبقي: {remaining_time}\n"

        status_message += f"\n💡 **ملاحظة:** النظام صارم - أي تحديث يمنع جميع التحديثات الأخرى لمدة 3 دقائق"
        status_message += f"\n🛡️ هذا يضمن عدم حدوث تضارب بين المستخدمين"

        # التحقق من نوع التحديث (زر أم أمر نصي)
        if update.callback_query:
            await update.callback_query.message.reply_text(status_message, parse_mode='Markdown')
        else:
            await update.message.reply_text(status_message, parse_mode='Markdown')

    except Exception as e:
        logger.error(f"خطأ في عرض حالة نظام التحكم في التوقيت: {e}")
        # التحقق من نوع التحديث (زر أم أمر نصي)
        if update.callback_query:
            await update.callback_query.message.reply_text("❌ حدث خطأ أثناء جلب حالة النظام")
        else:
            await update.message.reply_text("❌ حدث خطأ أثناء جلب حالة النظام")

def main():
    """تشغيل البوت"""
    # إنشاء التطبيق
    application = Application.builder().token(TOKEN).build()

    try:
        # التحقق من المتغيرات البيئية
        check_environment_variables()

        # تهيئة الملفات
        initialize_files()

        # بدء المجدول
        schedule_updates()

        # إضافة معالجات الأوامر
        application.add_handler(CommandHandler("start", start))
        application.add_handler(CommandHandler("ar", arabic_courses_command))
        application.add_handler(CommandHandler("check_new", check_new_command))
        application.add_handler(CommandHandler("vip_info", vip_info_command))
        application.add_handler(CommandHandler("add_vip", add_vip_command))
        application.add_handler(CommandHandler("remove_vip", remove_vip_command))
        application.add_handler(CommandHandler("extend_vip", extend_vip_command))
        application.add_handler(CommandHandler("test_notifications", test_notification_system))
        application.add_handler(CommandHandler("toggle_notifications", toggle_notifications_command))
        application.add_handler(CommandHandler("test_auto_check", test_auto_check_command))
        application.add_handler(CommandHandler("send_to_channel", send_to_channel_command))
        application.add_handler(CommandHandler("clean_courses", clean_courses_command))
        application.add_handler(CommandHandler("cooldown_status", cooldown_status_command))
        application.add_handler(CallbackQueryHandler(button))

        # إضافة معالج الأخطاء
        application.add_error_handler(error_handler)

        # بدء البوت
        logger.info("Starting bot...")
        logger.info("🚀 البوت جاهز - سيبدأ الفحص التلقائي كل 10 دقائق...")

        # تشغيل البوت في وضع الاستطلاع
        application.run_polling(allowed_updates=Update.ALL_TYPES)

        return application

    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        raise

if __name__ == '__main__':
    try:
        # تشغيل البوت بشكل مباشر
        main()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot crashed: {e}")