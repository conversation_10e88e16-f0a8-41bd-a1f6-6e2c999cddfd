# 🤖 بوت سحب المنشورات المتطور

بوت تليجرام متطور لسحب المنشورات من القنوات والمجموعات العامة بطرق مختلفة.

## ✨ الميزات

### 🎯 ميزات السحب
- **سحب منشور واحد** - سحب منشور محدد فقط
- **سحب نطاق محدد** - تحديد أول وآخر منشور للسحب
- **سحب تسلسلي للخلف** - من رقم معين إلى الأقدم
- **سحب تسلسلي للأمام** - من رقم معين إلى الأحدث
- **حماية من الحظر** - تأخير 3 ثوان بين المنشورات
- **فترات راحة** - راحة دقيقة كل 95 منشور

### 💎 نظام العضويات
- **عضوية مجانية** - 50 تحويل يومياً
- **عضوية مميزة** - تحويلات غير محدودة (1$ شهرياً فقط!)
- **إدارة تلقائية للحدود** - فحص وتحديث تلقائي
- **تتبع الاستخدام** - إحصائيات مفصلة لكل مستخدم
- **تفعيل فوري** - بعد التواصل مع @GurusVIP

### 🎛️ لوحة التحكم الإدارية
- **إدارة المستخدمين** - عرض وإدارة جميع المستخدمين
- **إدارة العضويات** - ترقية وتخفيض العضويات
- **الإحصائيات العامة** - تقارير شاملة عن البوت
- **إدارة الحظر** - حظر وإلغاء حظر المستخدمين
- **تسجيل الروابط** - تسجيل صامت لروابط المستخدمين

### 🔐 الأمان والحماية
- **اشتراك إجباري** - التحقق من الاشتراك في القنوات المطلوبة
- **نظام حدود ذكي** - منع تجاوز الحدود المسموحة
- **تشفير البيانات** - حماية معلومات المستخدمين

## 🛠️ التثبيت

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. إعداد متغيرات البيئة

انسخ ملف `.env.example` إلى `.env` وقم بتعبئة البيانات:

```bash
cp .env.example .env
```

ثم عدل ملف `.env`:

```env
API_ID=your_api_id_here
API_HASH=your_api_hash_here
BOT_TOKEN=your_bot_token_here
ADMIN_IDS=123456789,987654321
```

### 3. الحصول على البيانات المطلوبة

- **API_ID & API_HASH**: من [my.telegram.org](https://my.telegram.org)
- **BOT_TOKEN**: من [@BotFather](https://t.me/BotFather)
- **ADMIN_IDS**: معرفات المشرفين (اختياري)

### 4. تشغيل البوت

```bash
python bot.py
```

## 💎 العضوية المميزة

### 🌟 المميزات:
- **تحويلات غير محدودة يومياً** ∞
- **أولوية في المعالجة** ⚡
- **دعم فني مميز 24/7** 🛠️
- **ميزات حصرية قادمة** 🚀
- **بدون انتظار أو قيود** ⏱️

### 💰 السعر:
- **شهر كامل: 1$ فقط!** 🔥
- **أقل من 4 سنت في اليوم** 💸

### 📞 كيفية الاشتراك:
1. تواصل مع المطور [@GurusVIP](https://t.me/GurusVIP)
2. أرسل له "أريد العضوية المميزة"
3. ادفع 1$ عبر الطريقة المتفق عليها
4. **التفعيل فوري بعد الدفع!** ⚡

## 📋 الأوامر المتاحة

### للمستخدمين:
- `/start` - بدء استخدام البوت
- `/help` - عرض المساعدة
- `/cancel` - إلغاء العملية الحالية
- `/settings` - إعدادات البوت
- `/stats` - عرض إحصائيات السحب

### للمشرفين فقط:
- `/admin` - لوحة التحكم الرئيسية
- `/promote [user_id] [days]` - ترقية مستخدم للعضوية المميزة
- `/demote [user_id]` - تخفيض مستخدم للعضوية المجانية
- `/ban_user [user_id] [reason]` - حظر مستخدم
- `/unban_user [user_id]` - إلغاء حظر مستخدم
- `/user_info [user_id]` - عرض معلومات مستخدم
- `/admin_logs` - عرض إحصائيات الروابط
- `/exclude_link [رابط]` - إضافة رابط للاستثناء
- `/exclude_channel [قناة]` - إضافة قناة للاستثناء
- `/show_excluded` - عرض القوائم المستثناة

## ⚖️ سياسة الاستخدام

- البوت يعمل ضمن سياسة استخدام تليجرام
- يسحب فقط من القنوات والمجموعات العامة
- لا يملك صلاحيات للوصول إلى أي محتوى خاص
- يحترم حقوق النشر والملكية الفكرية

## 🔧 الإعدادات

يمكن تخصيص البوت من خلال تعديل المتغيرات في بداية ملف `bot.py`:

- `REQUIRED_CHANNELS` - قنوات الاشتراك الإجباري
- `EXCLUDED_LINKS` - روابط مستثناة من التسجيل
- `EXCLUDED_CHANNELS` - قنوات مستثناة من التسجيل

## 📝 ملاحظات مهمة

- يوجد تأخير 3 ثوان بين كل منشور للحماية من الحظر
- فترة راحة 1 دقيقة كل 95 منشور لضمان الأمان التام
- جميع المنشورات المسحوبة محفوظة بشكل دائم
- يتم تسجيل الروابط بصمت للمراقبة

## 🚨 استكشاف الأخطاء

### خطأ في الاستيراد:
```bash
pip install pyrogram python-dotenv TgCrypto
```

### خطأ في متغيرات البيئة:
تأكد من وجود ملف `.env` وأن جميع المتغيرات المطلوبة موجودة.

### خطأ في الصلاحيات:
تأكد من أن البوت لديه صلاحيات الوصول للقنوات المطلوبة.

## 👨‍💻 المطور

تم تطوير البوت بواسطة: [@GurusVIP](https://t.me/GurusVIP)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.
