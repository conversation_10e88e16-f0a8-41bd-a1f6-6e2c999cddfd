import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class VIPManager:
    """مدير نظام VIP المتطور"""

    def __init__(self, vip_file: str = "vip_users.json"):
        self.vip_file = vip_file
        self.vip_data: Dict = self._load_vip_data()

    def _load_vip_data(self) -> Dict:
        """تحميل بيانات المستخدمين VIP من الملف"""
        try:
            if os.path.exists(self.vip_file):
                with open(self.vip_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            # إنشاء ملف فارغ إذا لم يكن موجودًا
            with open(self.vip_file, "w", encoding="utf-8") as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            return {}
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات VIP: {e}")
            return {}

    def _save_vip_data(self) -> bool:
        """حفظ بيانات المستخدمين VIP إلى الملف"""
        try:
            with open(self.vip_file, "w", encoding="utf-8") as f:
                json.dump(self.vip_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات VIP: {e}")
            return False

    def is_vip(self, user_id: int) -> bool:
        """التحقق من حالة VIP للمستخدم"""
        try:
            user = self.vip_data.get(str(user_id))
            if not user:
                return False

            expiry = datetime.strptime(user["expires_at"], "%Y-%m-%d").date()
            return expiry >= datetime.now().date()
        except Exception as e:
            logger.error(f"خطأ في التحقق من حالة VIP للمستخدم {user_id}: {e}")
            return False

    def add_vip(self, user_id: int, name: str, days: int) -> bool:
        """إضافة مستخدم جديد كـ VIP"""
        try:
            joined_at = datetime.now().date()
            expires_at = joined_at + timedelta(days=days)

            self.vip_data[str(user_id)] = {
                "name": name,
                "joined_at": str(joined_at),
                "expires_at": str(expires_at),
                "days_total": days
            }

            return self._save_vip_data()
        except Exception as e:
            logger.error(f"خطأ في إضافة مستخدم VIP {user_id}: {e}")
            return False

    def remove_vip(self, user_id: int) -> bool:
        """حذف مستخدم VIP"""
        try:
            if str(user_id) in self.vip_data:
                del self.vip_data[str(user_id)]
                return self._save_vip_data()
            return False
        except Exception as e:
            logger.error(f"خطأ في حذف مستخدم VIP {user_id}: {e}")
            return False

    def extend_vip(self, user_id: int, days: int) -> bool:
        """تمديد عضوية VIP"""
        try:
            user = self.vip_data.get(str(user_id))
            if not user:
                return False

            current_expiry = datetime.strptime(user["expires_at"], "%Y-%m-%d").date()
            new_expiry = current_expiry + timedelta(days=days)

            user["expires_at"] = str(new_expiry)
            user["days_total"] = user.get("days_total", 0) + days

            return self._save_vip_data()
        except Exception as e:
            logger.error(f"خطأ في تمديد VIP للمستخدم {user_id}: {e}")
            return False

    def get_vip_info(self, user_id: int) -> Optional[Dict]:
        """الحصول على معلومات VIP للمستخدم"""
        return self.vip_data.get(str(user_id))

    def get_all_vip_users(self) -> Dict:
        """الحصول على جميع مستخدمي VIP"""
        return self.vip_data

    def get_vip_time_remaining(self, user_id: int) -> str:
        """حساب الوقت المتبقي للعضوية VIP"""
        user = self.vip_data.get(str(user_id))
        if not user:
            return "غير مشترك"

        try:
            expiry_date = datetime.strptime(user["expires_at"], "%Y-%m-%d").date()
            today = datetime.now().date()

            if today > expiry_date:
                return "منتهية"

            remaining = expiry_date - today
            days = remaining.days

            if days > 0:
                return f"{days} يوم"
            else:
                return "ينتهي اليوم"
        except Exception as e:
            logger.error(f"خطأ في حساب الوقت المتبقي: {e}")
            return "خطأ"

    def check_and_remove_expired(self) -> int:
        """فحص وحذف العضويات المنتهية"""
        expired_count = 0
        today = datetime.now().date()
        expired_users = []

        for user_id, user_data in self.vip_data.items():
            try:
                expiry_date = datetime.strptime(user_data["expires_at"], "%Y-%m-%d").date()
                if today > expiry_date:
                    expired_users.append(user_id)
                    expired_count += 1
            except Exception as e:
                logger.error(f"خطأ في فحص انتهاء VIP للمستخدم {user_id}: {e}")

        # حذف المستخدمين المنتهيين
        for user_id in expired_users:
            del self.vip_data[user_id]

        if expired_count > 0:
            self._save_vip_data()

        return expired_count

class UserDatabase:
    """نظام إدارة قاعدة بيانات المستخدمين والعضويات"""
    
    def __init__(self, db_file: str = "users_database.json"):
        self.db_file = db_file
        self.users_data = self._load_database()
    
    def _load_database(self) -> Dict[str, Any]:
        """تحميل قاعدة البيانات من الملف"""
        try:
            if os.path.exists(self.db_file):
                with open(self.db_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {"users": {}, "stats": {"total_users": 0, "premium_users": 0, "total_downloads": 0}}
        except Exception as e:
            print(f"خطأ في تحميل قاعدة البيانات: {e}")
            return {"users": {}, "stats": {"total_users": 0, "premium_users": 0, "total_downloads": 0}}
    
    def _save_database(self):
        """حفظ قاعدة البيانات في الملف"""
        try:
            with open(self.db_file, 'w', encoding='utf-8') as f:
                json.dump(self.users_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ قاعدة البيانات: {e}")
    
    def get_user(self, user_id: int) -> Dict[str, Any]:
        """الحصول على بيانات المستخدم"""
        user_id_str = str(user_id)
        if user_id_str not in self.users_data["users"]:
            # إنشاء مستخدم جديد
            self.users_data["users"][user_id_str] = {
                "user_id": user_id,
                "username": "",
                "first_name": "",
                "membership_type": "free",  # free, premium
                "downloads_count": 0,
                "daily_downloads": 0,
                "last_download_date": "",
                "registration_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "premium_expiry": None,
                "total_spent": 0,
                "last_activity": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "is_banned": False,
                "ban_reason": "",
                "subscription_channels": []
            }
            self.users_data["stats"]["total_users"] += 1
            self._save_database()
        
        return self.users_data["users"][user_id_str]
    
    def update_user_info(self, user_id: int, username: str = None, first_name: str = None):
        """تحديث معلومات المستخدم الأساسية"""
        user = self.get_user(user_id)
        if username:
            user["username"] = username
        if first_name:
            user["first_name"] = first_name
        user["last_activity"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self._save_database()
    
    def increment_downloads(self, user_id: int, vip_manager=None) -> bool:
        """زيادة عداد التحويلات وفحص الحدود"""
        user = self.get_user(user_id)
        today = datetime.now().strftime("%Y-%m-%d")

        # إعادة تعيين العداد اليومي إذا كان يوم جديد
        if user["last_download_date"] != today:
            user["daily_downloads"] = 0
            user["last_download_date"] = today

        # فحص عضوية VIP أولاً (إذا كان متوفراً)
        if vip_manager and vip_manager.is_vip(user_id):
            # أعضاء VIP لا يخضعون لحدود
            user["downloads_count"] += 1
            user["daily_downloads"] += 1
            user["last_activity"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.users_data["stats"]["total_downloads"] += 1
            self._save_database()
            return True

        # فحص الحدود للمستخدمين العاديين
        if user["membership_type"] == "free" and user["daily_downloads"] >= 50:
            return False  # تجاوز الحد المسموح

        # زيادة العدادات
        user["downloads_count"] += 1
        user["daily_downloads"] += 1
        user["last_activity"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.users_data["stats"]["total_downloads"] += 1

        self._save_database()
        return True
    
    def get_user_limits(self, user_id: int) -> Dict[str, Any]:
        """الحصول على حدود المستخدم الحالية"""
        user = self.get_user(user_id)
        today = datetime.now().strftime("%Y-%m-%d")
        
        # إعادة تعيين العداد اليومي إذا كان يوم جديد
        if user["last_download_date"] != today:
            user["daily_downloads"] = 0
            user["last_download_date"] = today
            self._save_database()
        
        if user["membership_type"] == "premium":
            return {
                "daily_limit": "غير محدود",
                "used_today": user["daily_downloads"],
                "remaining": "غير محدود",
                "membership": "مميز"
            }
        else:
            return {
                "daily_limit": 50,
                "used_today": user["daily_downloads"],
                "remaining": max(0, 50 - user["daily_downloads"]),
                "membership": "مجاني"
            }
    
    def upgrade_to_premium(self, user_id: int, days: int = 30):
        """ترقية المستخدم للعضوية المميزة"""
        user = self.get_user(user_id)
        old_membership = user["membership_type"]
        
        user["membership_type"] = "premium"
        expiry_date = datetime.now() + timedelta(days=days)
        user["premium_expiry"] = expiry_date.strftime("%Y-%m-%d %H:%M:%S")
        
        if old_membership == "free":
            self.users_data["stats"]["premium_users"] += 1
        
        self._save_database()
    
    def downgrade_to_free(self, user_id: int):
        """تخفيض المستخدم للعضوية المجانية"""
        user = self.get_user(user_id)
        if user["membership_type"] == "premium":
            user["membership_type"] = "free"
            user["premium_expiry"] = None
            self.users_data["stats"]["premium_users"] -= 1
            self._save_database()
    
    def check_premium_expiry(self, user_id: int) -> bool:
        """فحص انتهاء العضوية المميزة"""
        user = self.get_user(user_id)
        if user["membership_type"] == "premium" and user["premium_expiry"]:
            expiry_date = datetime.strptime(user["premium_expiry"], "%Y-%m-%d %H:%M:%S")
            if datetime.now() > expiry_date:
                self.downgrade_to_free(user_id)
                return True  # انتهت العضوية
        return False

    def get_premium_time_remaining(self, user_id: int) -> str:
        """حساب الوقت المتبقي للعضوية المميزة"""
        user = self.get_user(user_id)
        if user["membership_type"] == "premium" and user["premium_expiry"]:
            expiry_date = datetime.strptime(user["premium_expiry"], "%Y-%m-%d %H:%M:%S")
            now = datetime.now()

            if now > expiry_date:
                return "منتهية"

            remaining = expiry_date - now
            days = remaining.days
            hours = remaining.seconds // 3600

            if days > 0:
                return f"{days} يوم"
            elif hours > 0:
                return f"{hours} ساعة"
            else:
                minutes = (remaining.seconds % 3600) // 60
                return f"{minutes} دقيقة"

        return "غير مميز"

    def get_all_premium_users(self) -> list:
        """الحصول على جميع المستخدمين المميزين مع تفاصيلهم"""
        premium_users = []
        for user_id, user_data in self.users_data["users"].items():
            if user_data["membership_type"] == "premium":
                remaining_time = self.get_premium_time_remaining(int(user_id))
                premium_users.append({
                    "user_id": int(user_id),
                    "username": user_data.get("username", "غير معروف"),
                    "first_name": user_data.get("first_name", "غير معروف"),
                    "premium_expiry": user_data.get("premium_expiry", ""),
                    "remaining_time": remaining_time,
                    "downloads_count": user_data.get("downloads_count", 0),
                    "registration_date": user_data.get("registration_date", "")
                })

        # ترتيب حسب تاريخ الانتهاء
        premium_users.sort(key=lambda x: x["premium_expiry"])
        return premium_users

    def extend_premium(self, user_id: int, days: int):
        """تمديد العضوية المميزة"""
        user = self.get_user(user_id)

        if user["membership_type"] == "premium" and user["premium_expiry"]:
            # إذا كان مميز بالفعل، نمدد من تاريخ الانتهاء الحالي
            current_expiry = datetime.strptime(user["premium_expiry"], "%Y-%m-%d %H:%M:%S")
            new_expiry = current_expiry + timedelta(days=days)
        else:
            # إذا لم يكن مميز، نبدأ من الآن
            user["membership_type"] = "premium"
            new_expiry = datetime.now() + timedelta(days=days)
            if user["membership_type"] != "premium":
                self.users_data["stats"]["premium_users"] += 1

        user["premium_expiry"] = new_expiry.strftime("%Y-%m-%d %H:%M:%S")
        self._save_database()

    def check_all_expired_memberships(self):
        """فحص جميع العضويات المنتهية وإلغاؤها تلقائياً"""
        expired_count = 0
        for user_id in list(self.users_data["users"].keys()):
            if self.check_premium_expiry(int(user_id)):
                expired_count += 1
        return expired_count
    
    def ban_user(self, user_id: int, reason: str = ""):
        """حظر المستخدم"""
        user = self.get_user(user_id)
        user["is_banned"] = True
        user["ban_reason"] = reason
        self._save_database()
    
    def unban_user(self, user_id: int):
        """إلغاء حظر المستخدم"""
        user = self.get_user(user_id)
        user["is_banned"] = False
        user["ban_reason"] = ""
        self._save_database()
    
    def is_user_banned(self, user_id: int) -> bool:
        """فحص ما إذا كان المستخدم محظور"""
        user = self.get_user(user_id)
        return user.get("is_banned", False)
    
    def get_all_users(self) -> Dict[str, Any]:
        """الحصول على جميع المستخدمين"""
        return self.users_data["users"]
    
    def get_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات عامة"""
        # تحديث إحصائيات المستخدمين المميزين
        premium_count = sum(1 for user in self.users_data["users"].values() 
                          if user["membership_type"] == "premium")
        self.users_data["stats"]["premium_users"] = premium_count
        
        return self.users_data["stats"]
    
    def search_users(self, query: str) -> list:
        """البحث عن المستخدمين"""
        results = []
        for user_id, user_data in self.users_data["users"].items():
            if (query.lower() in user_data.get("username", "").lower() or 
                query.lower() in user_data.get("first_name", "").lower() or 
                query in user_id):
                results.append(user_data)
        return results
    
    def get_top_users(self, limit: int = 10) -> list:
        """الحصول على أكثر المستخدمين نشاطاً"""
        users = list(self.users_data["users"].values())
        users.sort(key=lambda x: x["downloads_count"], reverse=True)
        return users[:limit]

# إنشاء مثيل عام لقاعدة البيانات
user_db = UserDatabase()
