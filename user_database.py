import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

class UserDatabase:
    """نظام إدارة قاعدة بيانات المستخدمين والعضويات"""
    
    def __init__(self, db_file: str = "users_database.json"):
        self.db_file = db_file
        self.users_data = self._load_database()
    
    def _load_database(self) -> Dict[str, Any]:
        """تحميل قاعدة البيانات من الملف"""
        try:
            if os.path.exists(self.db_file):
                with open(self.db_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {"users": {}, "stats": {"total_users": 0, "premium_users": 0, "total_downloads": 0}}
        except Exception as e:
            print(f"خطأ في تحميل قاعدة البيانات: {e}")
            return {"users": {}, "stats": {"total_users": 0, "premium_users": 0, "total_downloads": 0}}
    
    def _save_database(self):
        """حفظ قاعدة البيانات في الملف"""
        try:
            with open(self.db_file, 'w', encoding='utf-8') as f:
                json.dump(self.users_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ قاعدة البيانات: {e}")
    
    def get_user(self, user_id: int) -> Dict[str, Any]:
        """الحصول على بيانات المستخدم"""
        user_id_str = str(user_id)
        if user_id_str not in self.users_data["users"]:
            # إنشاء مستخدم جديد
            self.users_data["users"][user_id_str] = {
                "user_id": user_id,
                "username": "",
                "first_name": "",
                "membership_type": "free",  # free, premium
                "downloads_count": 0,
                "daily_downloads": 0,
                "last_download_date": "",
                "registration_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "premium_expiry": None,
                "total_spent": 0,
                "last_activity": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "is_banned": False,
                "ban_reason": "",
                "subscription_channels": []
            }
            self.users_data["stats"]["total_users"] += 1
            self._save_database()
        
        return self.users_data["users"][user_id_str]
    
    def update_user_info(self, user_id: int, username: str = None, first_name: str = None):
        """تحديث معلومات المستخدم الأساسية"""
        user = self.get_user(user_id)
        if username:
            user["username"] = username
        if first_name:
            user["first_name"] = first_name
        user["last_activity"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self._save_database()
    
    def increment_downloads(self, user_id: int) -> bool:
        """زيادة عداد التحويلات وفحص الحدود"""
        user = self.get_user(user_id)
        today = datetime.now().strftime("%Y-%m-%d")
        
        # إعادة تعيين العداد اليومي إذا كان يوم جديد
        if user["last_download_date"] != today:
            user["daily_downloads"] = 0
            user["last_download_date"] = today
        
        # فحص الحدود
        if user["membership_type"] == "free" and user["daily_downloads"] >= 50:
            return False  # تجاوز الحد المسموح
        
        # زيادة العدادات
        user["downloads_count"] += 1
        user["daily_downloads"] += 1
        user["last_activity"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.users_data["stats"]["total_downloads"] += 1
        
        self._save_database()
        return True
    
    def get_user_limits(self, user_id: int) -> Dict[str, Any]:
        """الحصول على حدود المستخدم الحالية"""
        user = self.get_user(user_id)
        today = datetime.now().strftime("%Y-%m-%d")
        
        # إعادة تعيين العداد اليومي إذا كان يوم جديد
        if user["last_download_date"] != today:
            user["daily_downloads"] = 0
            user["last_download_date"] = today
            self._save_database()
        
        if user["membership_type"] == "premium":
            return {
                "daily_limit": "غير محدود",
                "used_today": user["daily_downloads"],
                "remaining": "غير محدود",
                "membership": "مميز"
            }
        else:
            return {
                "daily_limit": 50,
                "used_today": user["daily_downloads"],
                "remaining": max(0, 50 - user["daily_downloads"]),
                "membership": "مجاني"
            }
    
    def upgrade_to_premium(self, user_id: int, days: int = 30):
        """ترقية المستخدم للعضوية المميزة"""
        user = self.get_user(user_id)
        old_membership = user["membership_type"]
        
        user["membership_type"] = "premium"
        expiry_date = datetime.now() + timedelta(days=days)
        user["premium_expiry"] = expiry_date.strftime("%Y-%m-%d %H:%M:%S")
        
        if old_membership == "free":
            self.users_data["stats"]["premium_users"] += 1
        
        self._save_database()
    
    def downgrade_to_free(self, user_id: int):
        """تخفيض المستخدم للعضوية المجانية"""
        user = self.get_user(user_id)
        if user["membership_type"] == "premium":
            user["membership_type"] = "free"
            user["premium_expiry"] = None
            self.users_data["stats"]["premium_users"] -= 1
            self._save_database()
    
    def check_premium_expiry(self, user_id: int) -> bool:
        """فحص انتهاء العضوية المميزة"""
        user = self.get_user(user_id)
        if user["membership_type"] == "premium" and user["premium_expiry"]:
            expiry_date = datetime.strptime(user["premium_expiry"], "%Y-%m-%d %H:%M:%S")
            if datetime.now() > expiry_date:
                self.downgrade_to_free(user_id)
                return True  # انتهت العضوية
        return False
    
    def ban_user(self, user_id: int, reason: str = ""):
        """حظر المستخدم"""
        user = self.get_user(user_id)
        user["is_banned"] = True
        user["ban_reason"] = reason
        self._save_database()
    
    def unban_user(self, user_id: int):
        """إلغاء حظر المستخدم"""
        user = self.get_user(user_id)
        user["is_banned"] = False
        user["ban_reason"] = ""
        self._save_database()
    
    def is_user_banned(self, user_id: int) -> bool:
        """فحص ما إذا كان المستخدم محظور"""
        user = self.get_user(user_id)
        return user.get("is_banned", False)
    
    def get_all_users(self) -> Dict[str, Any]:
        """الحصول على جميع المستخدمين"""
        return self.users_data["users"]
    
    def get_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات عامة"""
        # تحديث إحصائيات المستخدمين المميزين
        premium_count = sum(1 for user in self.users_data["users"].values() 
                          if user["membership_type"] == "premium")
        self.users_data["stats"]["premium_users"] = premium_count
        
        return self.users_data["stats"]
    
    def search_users(self, query: str) -> list:
        """البحث عن المستخدمين"""
        results = []
        for user_id, user_data in self.users_data["users"].items():
            if (query.lower() in user_data.get("username", "").lower() or 
                query.lower() in user_data.get("first_name", "").lower() or 
                query in user_id):
                results.append(user_data)
        return results
    
    def get_top_users(self, limit: int = 10) -> list:
        """الحصول على أكثر المستخدمين نشاطاً"""
        users = list(self.users_data["users"].values())
        users.sort(key=lambda x: x["downloads_count"], reverse=True)
        return users[:limit]

# إنشاء مثيل عام لقاعدة البيانات
user_db = UserDatabase()
