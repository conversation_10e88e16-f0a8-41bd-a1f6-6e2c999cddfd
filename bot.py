import logging
import asyncio
import re
import os
import json
from datetime import datetime
from dotenv import load_dotenv
from pyrogram import Client, filters, enums
from pyrogram.types import (InlineKeyboardMarkup, InlineKeyboardButton,
                            CallbackQuery)
from user_database import user_db

# تحميل متغيرات البيئة من ملف .env
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('bot.log'),
              logging.StreamHandler()])
logger = logging.getLogger(__name__)

# التحقق من متغيرات البيئة المطلوبة
def validate_environment():
    """التحقق من وجود جميع متغيرات البيئة المطلوبة"""
    required_vars = ["API_ID", "API_HASH", "BOT_TOKEN"]
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please create a .env file with the required variables")
        exit(1)

# التحقق من متغيرات البيئة
validate_environment()

# بيانات البوت من متغيرات البيئة الآمنة
API_ID = int(os.getenv("API_ID"))
API_HASH = os.getenv("API_HASH")
BOT_TOKEN = os.getenv("BOT_TOKEN")
ADMIN_IDS = {int(admin_id.strip()) for admin_id in os.getenv("ADMIN_IDS", "").split(",") if admin_id.strip()} if os.getenv("ADMIN_IDS") else set()
user_states = {}
active_downloads = {}
range_data = {}  # لحفظ بيانات النطاق المحدد

# قنوات الاشتراك الإجباري
REQUIRED_CHANNELS = [
    {"username": "udmePro", "url": "https://t.me/udmePro"},
    {"username": "premuimfreex", "url": "https://t.me/premuimfreex"}
]

# ملف تسجيل الروابط (صامت)
LINKS_LOG_FILE = "user_links.js"

# روابط مستثناة من التسجيل
EXCLUDED_LINKS = [
    "https://t.me/news_channel/1234",
    "https://t.me/tech_channel/5678",
    # أضف المزيد من الروابط المستثناة هنا
]

# قنوات مستثناة من التسجيل
EXCLUDED_CHANNELS = [
    "news_channel",  # مثال: استثناء قناة كاملة
    # أضف المزيد من القنوات المستثناة هنا
]


def should_exclude_link(link):
    """فحص ما إذا كان الرابط يجب استثناؤه من التسجيل"""
    # فحص الروابط المستثناة مباشرة
    if link in EXCLUDED_LINKS:
        return True

    # فحص القنوات المستثناة
    try:
        channel_name = link.split('/')[-2]
        if channel_name in EXCLUDED_CHANNELS:
            return True
    except:
        pass

    return False


async def log_user_link_silently(user_id, username, first_name, link, action_type):
    """تسجيل رابط المستخدم بصمت في ملف JS"""
    try:
        # فحص الاستثناءات أولاً
        if should_exclude_link(link):
            return  # لا تسجل هذا الرابط
        # إنشاء بيانات الرابط
        link_data = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "user_id": user_id,
            "username": username or "لا يوجد",
            "first_name": first_name or "غير معروف",
            "link": link,
            "action": action_type,
            "channel": link.split('/')[-2] if '/' in link and len(link.split('/')) > 2 else "غير معروف",
            "message_id": link.split('/')[-1] if '/' in link and len(link.split('/')) > 1 else "غير معروف"
        }

        # قراءة البيانات الموجودة
        try:
            with open(LINKS_LOG_FILE, 'r', encoding='utf-8') as f:
                content = f.read()
                if content.strip():
                    # استخراج البيانات من بين علامات JSON
                    start_marker = "/*JSON_START*/"
                    end_marker = "/*JSON_END*/"
                    start = content.find(start_marker)
                    end = content.find(end_marker)

                    if start != -1 and end != -1:
                        start += len(start_marker)
                        json_data = content[start:end].strip()
                        existing_data = json.loads(json_data)
                    else:
                        # إذا لم توجد العلامات، جرب الطريقة القديمة
                        start = content.find('[')
                        end = content.rfind(']') + 1
                        if start != -1 and end != 0:
                            json_data = content[start:end]
                            existing_data = json.loads(json_data)
                        else:
                            existing_data = []
                else:
                    existing_data = []
        except (FileNotFoundError, json.JSONDecodeError):
            existing_data = []

        # إضافة البيانات الجديدة
        existing_data.append(link_data)

        # كتابة البيانات في تنسيق JS بدون تكرار
        json_data = json.dumps(existing_data, ensure_ascii=False, indent=2)

        js_content = f"""// ملف تسجيل روابط المستخدمين - تم إنشاؤه تلقائياً
// آخر تحديث: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

// بيانات الروابط في تنسيق JSON
/*JSON_START*/
{json_data}
/*JSON_END*/

// إحصائيات سريعة
const stats = {{
    totalLinks: {len(existing_data)},
    lastUpdate: "{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}",
    uniqueUsers: {len(set(item['user_id'] for item in existing_data))},
    uniqueChannels: {len(set(item['channel'] for item in existing_data if item['channel'] != 'غير معروف'))}
}};

// تحميل البيانات من JSON
const userLinks = {json_data};

// وظائف مساعدة
function getUserLinks(userId) {{
    return userLinks.filter(link => link.user_id === userId);
}}

function getChannelLinks(channelName) {{
    return userLinks.filter(link => link.channel === channelName);
}}

function getLinksToday() {{
    const today = new Date().toISOString().split('T')[0];
    return userLinks.filter(link => link.timestamp.startsWith(today));
}}

console.log('تم تحميل', userLinks.length, 'رابط من', stats.uniqueUsers, 'مستخدم');
"""

        # حفظ الملف
        with open(LINKS_LOG_FILE, 'w', encoding='utf-8') as f:
            f.write(js_content)

    except Exception as e:
        # تسجيل الخطأ بصمت دون إظهاره للمستخدم
        logging.error(f"Silent logging error: {e}")

# تكوين العميل
app = Client(
    "my_bot",
    api_id=API_ID,
    api_hash=API_HASH,
    bot_token=BOT_TOKEN,
    workdir=".",  # استخدام المجلد الحالي
    sleep_threshold=30  # زيادة وقت الانتظار بين الطلبات
)


async def check_user_subscription(client, user_id):
    """التحقق من اشتراك المستخدم في القنوات المطلوبة"""
    not_subscribed = []

    for channel in REQUIRED_CHANNELS:
        try:
            # محاولة الحصول على معلومات العضو
            member = await client.get_chat_member(channel["username"], user_id)

            # التحقق من حالة العضوية
            if member.status in ["left", "kicked", "banned"]:
                not_subscribed.append(channel)
            elif member.status == "restricted":
                # إذا كان محظور جزئياً، نعتبره غير مشترك
                not_subscribed.append(channel)

        except Exception as e:
            # إذا فشل التحقق (غالباً يعني أن المستخدم غير مشترك)
            logging.warning(f"Failed to check subscription for {user_id} in {channel['username']}: {e}")
            not_subscribed.append(channel)

    return not_subscribed


async def check_user_limits(user_id, username=None, first_name=None):
    """التحقق من حدود المستخدم وتحديث بياناته"""
    # تحديث معلومات المستخدم
    user_db.update_user_info(user_id, username, first_name)

    # فحص انتهاء العضوية المميزة
    user_db.check_premium_expiry(user_id)

    # فحص الحظر
    if user_db.is_user_banned(user_id):
        return False, "❌ تم حظرك من استخدام البوت"

    # فحص الحدود
    limits = user_db.get_user_limits(user_id)
    if limits["membership"] == "مجاني" and limits["remaining"] <= 0:
        return False, f"""
🚫 **تم تجاوز الحد المسموح اليومي**

📊 **حدودك الحالية:**
• العضوية: {limits["membership"]}
• الحد اليومي: {limits["daily_limit"]} تحويل
• المستخدم اليوم: {limits["used_today"]} تحويل
• المتبقي: {limits["remaining"]} تحويل

💎 **العضوية المميزة - 1$ شهرياً فقط!**
🌟 **المميزات:**
• تحويلات غير محدودة يومياً ∞
• أولوية في المعالجة ⚡
• دعم فني مميز 24/7

📞 **للاشتراك:**
تواصل مع @GurusVIP وقل "أريد العضوية المميزة"
"""

    return True, limits


async def send_limits_exceeded_message(message, limits_info):
    """إرسال رسالة تجاوز الحدود مع خيارات الترقية"""
    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("💎 الترقية للعضوية المميزة", callback_data="upgrade_premium")],
        [InlineKeyboardButton("📊 عرض حدودي", callback_data="show_my_limits")],
        [InlineKeyboardButton("👨‍💻 التواصل مع المطور", url="https://t.me/GurusVIP")],
        [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_menu")]
    ])

    await message.reply_text(limits_info, reply_markup=keyboard, parse_mode=enums.ParseMode.MARKDOWN)


async def send_subscription_message(message, not_subscribed_channels):
    """إرسال رسالة طلب الاشتراك"""
    buttons = []
    for channel in not_subscribed_channels:
        buttons.append([InlineKeyboardButton(f"📢 اشترك في {channel['username']}", url=channel["url"])])

    buttons.append([InlineKeyboardButton("✅ تحقق من الاشتراك", callback_data="check_subscription")])

    subscription_text = """
<b>🔒 يجب الاشتراك أولاً للاستخدام</b>

<b>📢 للاستفادة من البوت، يجب الاشتراك في القنوات التالية:</b>

<b>🔹 قناة التحديثات والدعم</b>
<b>🔹 قناة الأدوات المجانية</b>

<b>بعد الاشتراك، اضغط على "تحقق من الاشتراك" ✅</b>
"""

    await message.reply_text(subscription_text, reply_markup=InlineKeyboardMarkup(buttons), parse_mode=enums.ParseMode.HTML)


# تم حذف إعداد أوامر البوت لتبسيط الكود


@app.on_message(filters.command("start"))
async def start_command(client, message):
    user_id = message.from_user.id

    # التحقق من الاشتراك في القنوات المطلوبة
    not_subscribed = await check_user_subscription(client, user_id)
    if not_subscribed:
        await send_subscription_message(message, not_subscribed)
        return

    # تحديث بيانات المستخدم
    user_db.update_user_info(
        user_id,
        message.from_user.username,
        message.from_user.first_name
    )
    # الحصول على معلومات العضوية
    limits = user_db.get_user_limits(user_id)

    keyboard = InlineKeyboardMarkup([[
        InlineKeyboardButton("سحب منشور واحد 📥", callback_data="single_post")
    ], [
        InlineKeyboardButton("سحب نطاق محدد 📊", callback_data="range_posts")
    ], [
        InlineKeyboardButton("سحب تسلسلي للخلف ⬆️", callback_data="backward_posts")
    ], [
        InlineKeyboardButton("سحب تسلسلي للأمام ⬇️", callback_data="forward_posts")
    ], [
        InlineKeyboardButton("إحصائياتي 📊", callback_data="show_stats"),
        InlineKeyboardButton("حدودي 📋", callback_data="show_my_limits")
    ], [
        InlineKeyboardButton("💎 العضوية المميزة", callback_data="upgrade_premium")
    ], [
        InlineKeyboardButton("المطور 👨‍💻", url="https://t.me/GurusVIP")
    ]])

    # استخدم HTML مع تنسيق قوي لضمان ظهور النص بخط عريض
    welcome_text = f"""<b>🌟 مرحباً بك في بوت سحب المنشورات المتطور</b>

<b>👤 عضويتك: {limits["membership"]}</b>
<b>📊 حدودك اليومية: {limits["used_today"]}/{limits["daily_limit"]}</b>

✨ <b>الميزات المتاحة:</b>
• <b>سحب منشور واحد</b> - <b>سحب منشور محدد فقط</b> ⚡
• <b>سحب نطاق محدد</b> - <b>تحديد أول وآخر منشور للسحب</b> 📊
• <b>سحب تسلسلي للخلف</b> - <b>من رقم معين إلى الأقدم</b> ⬆️
• <b>سحب تسلسلي للأمام</b> - <b>من رقم معين إلى الأحدث</b> ⬇️
• <b>دعم جميع أنواع الوسائط</b> 📱
• <b>واجهة سهلة الاستخدام</b> 🎯

<b>📝 التعليمات:</b>
<b>1️⃣ اختر نوع السحب المطلوب</b>
<b>2️⃣ أرسل رابط المنشور/المنشورات</b>
<b>3️⃣ انتظر اكتمال العملية</b>

<b>⚖️ سياسة الاستخدام:</b>
<b>🔐 البوت يعمل ضمن سياسة استخدام تليجرام</b>
<b>✅ يسحب فقط من القنوات والمجموعات العامة</b>
<b>🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص</b>
<b>📢 يحترم حقوق النشر والملكية الفكرية</b>

<b>⏱️ ملاحظات مهمة:</b>
<b>🛡️ يوجد تأخير 3 ثوان بين كل منشور للحماية من الحظر</b>
<b>⏸️ فترة راحة 1 دقيقة كل 95 منشور لضمان الأمان التام</b>"""

    try:
        await message.reply_photo(photo="https://c.top4top.io/p_3454vqo5e1.jpg",
                                  caption=welcome_text,
                                  reply_markup=keyboard,
                                  parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        # إذا فشل HTML، جرب Markdown
        logger.warning(f"HTML failed: {e}, trying Markdown")
        markdown_text = """**🌟 مرحباً بك في بوت سحب المنشورات المتطور**

✨ **الميزات المتاحة:**
• **سحب منشور واحد** - **سحب منشور محدد فقط** ⚡
• **سحب نطاق محدد** - **تحديد أول وآخر منشور للسحب** 📊
• **سحب تسلسلي للخلف** - **من رقم معين إلى الأقدم** ⬆️
• **سحب تسلسلي للأمام** - **من رقم معين إلى الأحدث** ⬇️
• **دعم جميع أنواع الوسائط** 📱
• **واجهة سهلة الاستخدام** 🎯

**📝 التعليمات:**
**1️⃣ اختر نوع السحب المطلوب**
**2️⃣ أرسل رابط المنشور/المنشورات**
**3️⃣ انتظر اكتمال العملية**

**⚖️ سياسة الاستخدام:**
**🔐 البوت يعمل ضمن سياسة استخدام تليجرام**
**✅ يسحب فقط من القنوات والمجموعات العامة**
**🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص**"""

        try:
            await message.reply_photo(photo="https://c.top4top.io/p_3454vqo5e1.jpg",
                                      caption=markdown_text,
                                      reply_markup=keyboard,
                                      parse_mode=enums.ParseMode.MARKDOWN)
        except Exception as e2:
            # إذا فشل كل شيء، أرسل بدون تنسيق
            logger.warning(f"Markdown also failed: {e2}, sending plain text")
            plain_text = """🌟 مرحباً بك في بوت سحب المنشورات المتطور

✨ الميزات المتاحة:
• سحب منشور واحد - سحب منشور محدد فقط ⚡
• سحب نطاق محدد - تحديد أول وآخر منشور للسحب 📊
• سحب تسلسلي للخلف - من رقم معين إلى الأقدم ⬆️
• سحب تسلسلي للأمام - من رقم معين إلى الأحدث ⬇️
• دعم جميع أنواع الوسائط 📱
• واجهة سهلة الاستخدام 🎯

📝 التعليمات:
1️⃣ اختر نوع السحب المطلوب
2️⃣ أرسل رابط المنشور/المنشورات
3️⃣ انتظر اكتمال العملية

⚖️ سياسة الاستخدام:
🔐 البوت يعمل ضمن سياسة استخدام تليجرام
✅ يسحب فقط من القنوات والمجموعات العامة
🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص"""

            await message.reply_photo(photo="https://c.top4top.io/p_3454vqo5e1.jpg",
                                      caption=plain_text,
                                      reply_markup=keyboard)


# معالج واحد موحد للأزرار
@app.on_callback_query(
    filters.regex(
        "^(single_post|range_posts|forward_posts|backward_posts|stop_download|back_to_menu|show_stats|delete_now|check_subscription|show_my_limits|upgrade_premium|admin_users|admin_memberships|admin_stats|admin_links|admin_settings|admin_bans|admin_vip_panel|detailed_vip_list|refresh_vip_panel)$"
    ))
async def handle_buttons(client: Client, callback: CallbackQuery):
    try:
        user_id = callback.from_user.id
        data = callback.data

        # إضافة معالجة زر delete_now
        if data == "delete_now":
            await callback.message.delete()
            return

        # معالجة زر التحقق من الاشتراك
        if data == "check_subscription":
            not_subscribed = await check_user_subscription(client, user_id)
            if not_subscribed:
                await callback.answer("❌ يجب الاشتراك في جميع القنوات أولاً!", show_alert=True)
                return
            else:
                await callback.answer("✅ تم التحقق بنجاح! يمكنك الآن استخدام البوت", show_alert=True)
                # إعادة توجيه للقائمة الرئيسية
                await start_command(client, callback.message)
                return

        # التحقق من الاشتراك قبل أي عملية
        not_subscribed = await check_user_subscription(client, user_id)
        if not_subscribed:
            await callback.answer("❌ يجب الاشتراك في جميع القنوات للاستخدام!", show_alert=True)
            await send_subscription_message(callback.message, not_subscribed)
            return

        if data == "single_post":
            await callback.message.edit_text(
                "<b>قم بإرسال رابط المنشور الذي تريد سحبه</b>\n"
                "<b>مثال: https://t.me/channel_name/123</b>",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 رجوع",
                                         callback_data="back_to_menu")
                ]]),
                parse_mode=enums.ParseMode.HTML)
            # تعيين حالة المستخدم لسحب منشور واحد
            user_states[user_id] = "waiting_for_single_post"

        elif data == "range_posts":
            await callback.message.edit_text(
                "<b>سحب نطاق محدد من المنشورات</b>\n\n"
                "<b>قم بإرسال رابط أول منشور تريد البدء منه:</b>\n"
                "<b>مثال: https://t.me/channel_name/100</b>\n\n"
                "<b>بعدها سيُطلب منك رابط آخر منشور</b>",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 رجوع",
                                         callback_data="back_to_menu")
                ]]),
                parse_mode=enums.ParseMode.HTML)
            user_states[user_id] = "waiting_for_range_start"

        elif data == "forward_posts":
            await callback.message.edit_text(
                "<b>سحب تسلسلي للأمام</b>\n\n"
                "<b>قم بإرسال رابط المنشور الذي تريد البدء منه:</b>\n"
                "<b>مثال: https://t.me/channel_name/3605</b>\n\n"
                "<b>سيتم سحب المنشورات تسلسلياً من هذا الرقم للأمام</b>",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 رجوع",
                                         callback_data="back_to_menu")
                ]]),
                parse_mode=enums.ParseMode.HTML)
            user_states[user_id] = "waiting_for_forward_posts"

        elif data == "backward_posts":
            await callback.message.edit_text(
                "<b>سحب تسلسلي للخلف</b>\n\n"
                "<b>قم بإرسال رابط المنشور الذي تريد البدء منه:</b>\n"
                "<b>مثال: https://t.me/channel_name/3605</b>\n\n"
                "<b>سيتم سحب المنشورات تسلسلياً من هذا الرقم للخلف</b>",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 رجوع",
                                         callback_data="back_to_menu")
                ]]),
                parse_mode=enums.ParseMode.HTML)
            user_states[user_id] = "waiting_for_backward_posts"

        elif data == "stop_download":
            if user_id in active_downloads:
                active_downloads[user_id] = False
                await callback.answer("تم إيقاف عملية السحب! ✅")
            else:
                await callback.answer("لا توجد عملية سحب نشطة! ❌")

        elif data == "back_to_menu":
            # إزالة حالة المستخدم عند العودة للقائمة الرئيسية
            user_states.pop(user_id, None)
            keyboard = InlineKeyboardMarkup([[
                InlineKeyboardButton("سحب منشور واحد 📥", callback_data="single_post")
            ], [
                InlineKeyboardButton("سحب نطاق محدد 📊", callback_data="range_posts")
            ], [
                InlineKeyboardButton("سحب تسلسلي للخلف ⬆️", callback_data="backward_posts")
            ], [
                InlineKeyboardButton("سحب تسلسلي للأمام ⬇️", callback_data="forward_posts")
            ], [
                InlineKeyboardButton("إحصائيات 📊", callback_data="show_stats")
            ], [
                InlineKeyboardButton("المطور 👨‍💻", url="https://t.me/GurusVIP")
            ]])

            welcome_text = """
<b>🌟 مرحباً بك في بوت سحب المنشورات المتطور</b>

<b>✨ الميزات المتاحة:</b>
<b>• سحب منشور واحد - سحب منشور محدد فقط ⚡</b>
<b>• سحب نطاق محدد - تحديد أول وآخر منشور للسحب 📊</b>
<b>• سحب تسلسلي للخلف - من رقم معين إلى الأقدم ⬆️</b>
<b>• سحب تسلسلي للأمام - من رقم معين إلى الأحدث ⬇️</b>

<b>⚖️ سياسة الاستخدام:</b>
<b>🔐 البوت يعمل ضمن سياسة استخدام تليجرام</b>
<b>✅ يسحب فقط من القنوات والمجموعات العامة</b>
<b>🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص</b>

<b>اختر الخيار المناسب لك:</b>
"""
            await callback.message.edit_text(welcome_text, reply_markup=keyboard, parse_mode=enums.ParseMode.HTML)

        elif data == "show_stats":
            # عرض إحصائيات المستخدم العادي
            limits = user_db.get_user_limits(user_id)
            user_data = user_db.get_user(user_id)

            stats_text = f"""
<b>📊 إحصائياتك الشخصية:</b>

<b>👤 معلومات العضوية:</b>
<b>• نوع العضوية: {limits["membership"]}</b>
<b>• الحد اليومي: {limits["daily_limit"]}</b>
<b>• المستخدم اليوم: {limits["used_today"]}</b>
<b>• المتبقي: {limits["remaining"]}</b>

<b>📈 إحصائيات الاستخدام:</b>
<b>• إجمالي التحويلات: {user_data["downloads_count"]}</b>
<b>• تاريخ التسجيل: {user_data["registration_date"]}</b>
<b>• آخر نشاط: {user_data["last_activity"]}</b>

<b>🚀 تم تطوير البوت بواسطة: @GurusVIP</b>
"""
            await callback.message.edit_text(stats_text, parse_mode=enums.ParseMode.HTML)

        elif data == "show_my_limits":
            limits = user_db.get_user_limits(user_id)
            user_data = user_db.get_user(user_id)

            limits_text = f"""
<b>📊 حدودك الحالية:</b>

<b>💎 نوع العضوية: {limits["membership"]}</b>
<b>📅 الحد اليومي: {limits["daily_limit"]}</b>
<b>✅ المستخدم اليوم: {limits["used_today"]}</b>
<b>⏳ المتبقي: {limits["remaining"]}</b>

<b>📈 إجمالي التحويلات: {user_data["downloads_count"]}</b>
"""

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("� ترقية للعضوية المميزة", callback_data="upgrade_premium")],
                [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_menu")]
            ])

            await callback.message.edit_text(limits_text, reply_markup=keyboard, parse_mode=enums.ParseMode.HTML)

        elif data == "upgrade_premium":
            upgrade_text = """
<b>💎 العضوية المميزة</b>

<b>🌟 مميزات العضوية المميزة:</b>
<b>• تحويلات غير محدودة يومياً ∞</b>
<b>• أولوية في المعالجة ⚡</b>
<b>• دعم فني مميز 24/7</b>
<b>• ميزات حصرية قادمة 🚀</b>
<b>• بدون انتظار أو قيود</b>

<b>💰 السعر الخاص:</b>
<b>🔥 شهر كامل: 1$ فقط!</b>
<b>💸 أقل من 4 سنت في اليوم</b>

<b>📞 للاشتراك:</b>
<b>تواصل مع المطور @GurusVIP</b>
<b>وأرسل له "أريد العضوية المميزة"</b>

<b>⚡ التفعيل فوري بعد الدفع!</b>
"""

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("� تواصل مع @GurusVIP", url="https://t.me/GurusVIP")],
                [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_menu")]
            ])

            await callback.message.edit_text(upgrade_text, reply_markup=keyboard, parse_mode=enums.ParseMode.HTML)

        # معالجة أزرار لوحة التحكم الإدارية
        elif data.startswith("admin_") and user_id in ADMIN_IDS:
            await handle_admin_buttons(callback, data)

        # معالجة أزرار VIP
        elif data in ["admin_vip_panel", "detailed_vip_list", "refresh_vip_panel"] and user_id in ADMIN_IDS:
            await handle_vip_buttons(callback, data)

        await callback.answer()

    except Exception as e:
        logging.error(f"Error in callback handler: {e}")
        await callback.answer("حدث خطأ! ❌")


async def handle_admin_buttons(callback, data):
    """معالجة أزرار لوحة التحكم الإدارية"""
    try:
        if data == "admin_users":
            stats = user_db.get_stats()
            top_users = user_db.get_top_users(5)

            users_text = f"""
<b>👥 إدارة المستخدمين</b>

<b>📊 إحصائيات عامة:</b>
<b>• إجمالي المستخدمين: {stats["total_users"]}</b>
<b>• المستخدمين المميزين: {stats["premium_users"]}</b>
<b>• إجمالي التحويلات: {stats["total_downloads"]}</b>

<b>🏆 أكثر المستخدمين نشاطاً:</b>
"""

            for i, user in enumerate(top_users, 1):
                username = user.get("username", "غير معروف")
                name = user.get("first_name", "غير معروف")
                downloads = user.get("downloads_count", 0)
                membership = user.get("membership_type", "free")
                users_text += f"<b>{i}. {name} (@{username}) - {downloads} تحويل - {membership}</b>\n"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("🔍 البحث عن مستخدم", callback_data="search_user")],
                [InlineKeyboardButton("📋 قائمة المستخدمين", callback_data="list_users")],
                [InlineKeyboardButton("🔙 رجوع للوحة التحكم", callback_data="back_to_admin")]
            ])

            await callback.message.edit_text(users_text, reply_markup=keyboard, parse_mode=enums.ParseMode.HTML)

        elif data == "admin_stats":
            stats = user_db.get_stats()
            all_users = user_db.get_all_users()

            # حساب إحصائيات إضافية
            active_today = sum(1 for user in all_users.values()
                             if user.get("last_download_date") == datetime.now().strftime("%Y-%m-%d"))

            stats_text = f"""
<b>📊 الإحصائيات العامة للبوت</b>

<b>👥 المستخدمين:</b>
<b>• إجمالي المستخدمين: {stats["total_users"]}</b>
<b>• المستخدمين المميزين: {stats["premium_users"]}</b>
<b>• المستخدمين المجانيين: {stats["total_users"] - stats["premium_users"]}</b>
<b>• النشطين اليوم: {active_today}</b>

<b>📈 الاستخدام:</b>
<b>• إجمالي التحويلات: {stats["total_downloads"]}</b>
<b>• متوسط التحويلات لكل مستخدم: {stats["total_downloads"] // max(stats["total_users"], 1)}</b>

<b>💰 العضويات:</b>
<b>• نسبة المستخدمين المميزين: {(stats["premium_users"] / max(stats["total_users"], 1) * 100):.1f}%</b>
"""

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("🔄 تحديث الإحصائيات", callback_data="admin_stats")],
                [InlineKeyboardButton("🔙 رجوع للوحة التحكم", callback_data="back_to_admin")]
            ])

            await callback.message.edit_text(stats_text, reply_markup=keyboard, parse_mode=enums.ParseMode.HTML)

        elif data == "admin_memberships":
            stats = user_db.get_stats()
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("⬆️ ترقية مستخدم", callback_data="promote_user")],
                [InlineKeyboardButton("⬇️ تخفيض مستخدم", callback_data="demote_user")],
                [InlineKeyboardButton("📋 قائمة المميزين", callback_data="list_premium")],
                [InlineKeyboardButton("🔙 رجوع للوحة التحكم", callback_data="back_to_admin")]
            ])

            memberships_text = f"""
<b>💎 إدارة العضويات</b>

<b>📊 إحصائيات العضويات:</b>
<b>• إجمالي المستخدمين: {stats["total_users"]}</b>
<b>• المستخدمين المميزين: {stats["premium_users"]}</b>
<b>• المستخدمين المجانيين: {stats["total_users"] - stats["premium_users"]}</b>

<b>💰 السعر الحالي: 1$ شهرياً</b>
<b>📞 التواصل: @GurusVIP</b>

<b>🛠️ العمليات المتاحة:</b>
<b>⬆️ ترقية مستخدم - ترقية للعضوية المميزة</b>
<b>⬇️ تخفيض مستخدم - تخفيض للعضوية المجانية</b>
<b>📋 قائمة المميزين - عرض جميع المستخدمين المميزين</b>

<b>💡 استخدم الأوامر:</b>
<b>/promote [user_id] [days] - ترقية مستخدم</b>
<b>/demote [user_id] - تخفيض مستخدم</b>
"""

            await callback.message.edit_text(memberships_text, reply_markup=keyboard, parse_mode=enums.ParseMode.HTML)

        elif data == "back_to_admin":
            # العودة للوحة التحكم الرئيسية
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("👥 إدارة المستخدمين", callback_data="admin_users")],
                [InlineKeyboardButton("💎 إدارة العضويات", callback_data="admin_memberships")],
                [InlineKeyboardButton("🌟 لوحة VIP", callback_data="admin_vip_panel")],
                [InlineKeyboardButton("📊 الإحصائيات العامة", callback_data="admin_stats")],
                [InlineKeyboardButton("🔗 سجل الروابط", callback_data="admin_links")],
                [InlineKeyboardButton("⚙️ إعدادات البوت", callback_data="admin_settings")],
                [InlineKeyboardButton("🚫 إدارة الحظر", callback_data="admin_bans")]
            ])

            admin_text = """
🎛️ **لوحة التحكم الإدارية**

مرحباً بك في لوحة التحكم الشاملة للبوت
اختر القسم الذي تريد إدارته:

🌟 **جديد: لوحة VIP** - إدارة متقدمة لأعضاء VIP
"""

            await callback.message.edit_text(admin_text, reply_markup=keyboard, parse_mode=enums.ParseMode.MARKDOWN)

    except Exception as e:
        logging.error(f"Error in admin buttons handler: {e}")
        await callback.answer("حدث خطأ في لوحة التحكم! ❌")


async def handle_vip_buttons(callback, data):
    """معالجة أزرار لوحة VIP"""
    try:
        if data == "admin_vip_panel" or data == "refresh_vip_panel":
            # إعادة عرض لوحة VIP محدثة
            expired_count = user_db.check_all_expired_memberships()
            premium_users = user_db.get_all_premium_users()

            panel_text = f"""
<b>💎 لوحة إدارة أعضاء VIP</b>

<b>📊 الإحصائيات:</b>
<b>• إجمالي الأعضاء: {len(premium_users)}</b>
<b>• الأعضاء النشطين: {len([u for u in premium_users if u['remaining_time'] != 'منتهية'])}</b>
<b>• الاشتراكات المنتهية: {len([u for u in premium_users if u['remaining_time'] == 'منتهية'])}</b>

<b>🔄 تم فحص وإلغاء {expired_count} عضوية منتهية</b>

<b>👥 قائمة أعضاء VIP:</b>
"""

            if not premium_users:
                panel_text += "<b>لا يوجد أعضاء VIP حالياً</b>"
            else:
                for i, user in enumerate(premium_users[:10], 1):
                    status = "✅" if user['remaining_time'] != 'منتهية' else "❌"
                    name = user['first_name'][:15] + "..." if len(user['first_name']) > 15 else user['first_name']
                    panel_text += f"<b>{i}. {status} {name} ({user['remaining_time']})</b>\n"

                if len(premium_users) > 10:
                    panel_text += f"<b>... و {len(premium_users) - 10} عضو آخر</b>\n"

            panel_text += f"""

<b>🛠️ أوامر الإدارة:</b>
<b>/add_vip [ID] [أيام] - إضافة عضو VIP</b>
<b>/remove_vip [ID] - حذف عضو VIP</b>
<b>/extend_vip [ID] [أيام] - تمديد اشتراك</b>
<b>/vip_panel - عرض هذه اللوحة</b>
"""

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("📋 قائمة VIP مفصلة", callback_data="detailed_vip_list")],
                [InlineKeyboardButton("🔄 تحديث القائمة", callback_data="refresh_vip_panel")],
                [InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="back_to_admin")]
            ])

            await callback.message.edit_text(panel_text, reply_markup=keyboard, parse_mode=enums.ParseMode.HTML)

        elif data == "detailed_vip_list":
            # فحص العضويات المنتهية أولاً
            expired_count = user_db.check_all_expired_memberships()
            premium_users = user_db.get_all_premium_users()

            if not premium_users:
                list_text = """
<b>📋 قائمة أعضاء VIP المفصلة</b>

<b>❌ لا يوجد أعضاء VIP حالياً</b>

<b>💡 لإضافة عضو VIP جديد:</b>
<b>/add_vip [معرف_المستخدم] [عدد_الأيام]</b>
"""
            else:
                list_text = f"""
<b>📋 قائمة أعضاء VIP المفصلة</b>

<b>🔄 تم فحص وإلغاء {expired_count} عضوية منتهية</b>

<b>👥 الأعضاء ({len(premium_users)}):</b>

"""

                for i, user in enumerate(premium_users, 1):
                    status_icon = "✅" if user['remaining_time'] != 'منتهية' else "❌"
                    name = user['first_name'][:20] + "..." if len(user['first_name']) > 20 else user['first_name']
                    username = f"@{user['username']}" if user['username'] != 'غير معروف' else "لا يوجد"

                    list_text += f"""
<b>{i}. {status_icon} {name}</b>
<b>   🆔 المعرف: {user['user_id']}</b>
<b>   👤 المستخدم: {username}</b>
<b>   ⏰ المتبقي: {user['remaining_time']}</b>
<b>   📅 ينتهي: {user['premium_expiry'][:10]}</b>
<b>   📊 التحويلات: {user['downloads_count']}</b>
<b>   ━━━━━━━━━━━━━━━━━━━━</b>
"""

                list_text += f"""
<b>🛠️ أوامر سريعة:</b>
<b>/extend_vip [ID] [أيام] - تمديد</b>
<b>/remove_vip [ID] - حذف</b>
"""

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("🔄 تحديث القائمة", callback_data="detailed_vip_list")],
                [InlineKeyboardButton("🔙 رجوع للوحة VIP", callback_data="refresh_vip_panel")]
            ])

            await callback.message.edit_text(list_text, reply_markup=keyboard, parse_mode=enums.ParseMode.HTML)



    except Exception as e:
        logging.error(f"Error in VIP buttons handler: {e}")
        await callback.answer("حدث خطأ في لوحة VIP! ❌")


# تم حذف تسجيل معالج الأزرار لأن @app.on_callback_query يقوم بذلك تلقائياً


# إضافة معالجات للأوامر
@app.on_message(filters.command("help"))
async def help_command(_, message):
    help_text = """
<b>🔍 دليل استخدام البوت:</b>

<b>1️⃣ سحب منشور واحد:</b>
<b>- اختر "سحب منشور واحد 📥"</b>
<b>- أرسل رابط المنشور المراد سحبه</b>

<b>2️⃣ سحب نطاق محدد:</b>
<b>- اختر "سحب نطاق محدد 📊"</b>
<b>- أرسل رابط أول منشور</b>
<b>- أرسل رابط آخر منشور</b>
<b>- سيتم سحب جميع المنشورات في النطاق</b>

<b>3️⃣ سحب تسلسلي للخلف:</b>
<b>- اختر "سحب تسلسلي للخلف ⬆️"</b>
<b>- أرسل رابط منشور البداية</b>
<b>- سيتم السحب من هذا الرقم للخلف</b>

<b>4️⃣ سحب تسلسلي للأمام:</b>
<b>- اختر "سحب تسلسلي للأمام ⬇️"</b>
<b>- أرسل رابط منشور البداية</b>
<b>- سيتم السحب من هذا الرقم للأمام</b>

<b>5️⃣ الأوامر المتاحة:</b>
<b>/start - بدء استخدام البوت</b>
<b>/help - عرض هذه المساعدة</b>
<b>/cancel - إلغاء العملية الحالية</b>
<b>/settings - إعدادات البوت</b>
<b>/stats - عرض إحصائيات السحب</b>

<b>⚖️ سياسة الاستخدام:</b>
<b>🔐 البوت يعمل ضمن سياسة استخدام تليجرام</b>
<b>✅ يسحب فقط من القنوات والمجموعات العامة</b>
<b>🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص</b>
<b>📢 يحترم حقوق النشر والملكية الفكرية</b>

<b>⏱️ ملاحظات مهمة:</b>
<b>🛡️ يوجد تأخير 3 ثوان بين كل منشور للحماية من الحظر</b>
<b>⏸️ فترة راحة 1 دقيقة كل 95 منشور لضمان الأمان التام</b>

"""
    await message.reply_text(help_text, parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("settings"))
async def settings_command(_, message):
    settings_text = """
<b>⚙️ إعدادات البوت:</b>

<b>🔹 لا توجد إعدادات متاحة حالياً</b>
<b>🔸 سيتم إضافة المزيد من الإعدادات قريباً</b>

<b>⚖️ سياسة الاستخدام:</b>
<b>🔐 البوت يعمل ضمن سياسة استخدام تليجرام</b>
<b>✅ يسحب فقط من القنوات والمجموعات العامة</b>
<b>🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص</b>


"""
    await message.reply_text(settings_text, parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("stats"))
async def stats_command(_, message):
    stats_text = """
<b>📊 إحصائيات البوت:</b>

<b>🤖 البوت يعمل بشكل طبيعي</b>
<b>✅ جميع الميزات متاحة</b>
<b>🔄 جاهز لسحب المنشورات</b>

<b>⚖️ سياسة الاستخدام:</b>
<b>🔐 البوت يعمل ضمن سياسة استخدام تليجرام</b>
<b>✅ يسحب فقط من القنوات والمجموعات العامة</b>
<b>🚫 لا يملك صلاحيات للوصول إلى أي محتوى خاص</b>


"""
    await message.reply_text(stats_text, parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("cancel"))
async def cancel_command(_, message):
    user_id = message.from_user.id
    if user_id in active_downloads:
        active_downloads[user_id] = False
        user_states.pop(user_id, None)
        await message.reply_text("<b>✅ تم إلغاء العملية الحالية</b>", parse_mode=enums.ParseMode.HTML)
    else:
        await message.reply_text("<b>❌ لا توجد عملية نشطة للإلغاء</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("admin") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def admin_panel_command(_, message):
    """لوحة التحكم الرئيسية للمشرف"""
    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("👥 إدارة المستخدمين", callback_data="admin_users")],
        [InlineKeyboardButton("💎 إدارة العضويات", callback_data="admin_memberships")],
        [InlineKeyboardButton("🌟 لوحة VIP", callback_data="admin_vip_panel")],
        [InlineKeyboardButton("📊 الإحصائيات العامة", callback_data="admin_stats")],
        [InlineKeyboardButton("🔗 سجل الروابط", callback_data="admin_links")],
        [InlineKeyboardButton("⚙️ إعدادات البوت", callback_data="admin_settings")],
        [InlineKeyboardButton("🚫 إدارة الحظر", callback_data="admin_bans")]
    ])

    admin_text = """
🎛️ **لوحة التحكم الإدارية**

مرحباً بك في لوحة التحكم الشاملة للبوت
اختر القسم الذي تريد إدارته:

👥 **إدارة المستخدمين** - عرض وإدارة المستخدمين
💎 **إدارة العضويات** - ترقية وتخفيض العضويات
📊 **الإحصائيات العامة** - إحصائيات شاملة للبوت
🔗 **سجل الروابط** - عرض سجل الروابط المسحوبة
⚙️ **إعدادات البوت** - تخصيص إعدادات البوت
🚫 **إدارة الحظر** - حظر وإلغاء حظر المستخدمين
"""

    await message.reply_text(admin_text, reply_markup=keyboard, parse_mode=enums.ParseMode.MARKDOWN)


@app.on_message(filters.command("admin_logs") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def admin_logs_command(_, message):
    """عرض إحصائيات الروابط للمشرف فقط"""
    try:
        with open(LINKS_LOG_FILE, 'r', encoding='utf-8') as f:
            content = f.read()

        # استخراج البيانات من ملف JS
        start_marker = "/*JSON_START*/"
        end_marker = "/*JSON_END*/"
        start = content.find(start_marker)
        end = content.find(end_marker)

        if start != -1 and end != -1:
            start += len(start_marker)
            json_data = content[start:end].strip()
            links_data = json.loads(json_data)
        else:
            # إذا لم توجد العلامات، جرب الطريقة القديمة
            start = content.find('[')
            end = content.rfind(']') + 1
            if start != -1 and end != 0:
                json_data = content[start:end]
                links_data = json.loads(json_data)
            else:
                links_data = []

        if not links_data:
            await message.reply_text("<b>📊 لا توجد روابط مسجلة بعد</b>", parse_mode=enums.ParseMode.HTML)
            return

        # إحصائيات
        total_links = len(links_data)
        unique_users = len(set(item['user_id'] for item in links_data))
        unique_channels = len(set(item['channel'] for item in links_data if item['channel'] != 'غير معروف'))

        # آخر 10 روابط
        recent_links = links_data[-10:]

        admin_text = f"""
<b>🔍 إحصائيات الروابط المسجلة</b>

<b>📊 الإحصائيات العامة:</b>
<b>🔗 إجمالي الروابط: {total_links}</b>
<b>👥 عدد المستخدمين: {unique_users}</b>
<b>📺 عدد القنوات: {unique_channels}</b>

<b>📝 آخر 10 روابط:</b>
"""

        for link in recent_links:
            channel = link['channel'][:15] + "..." if len(link['channel']) > 15 else link['channel']
            username = link['username'][:10] + "..." if len(link['username']) > 10 else link['username']
            admin_text += f"<b>• {username} - {channel} - {link['timestamp'].split()[1][:5]}</b>\n"

        admin_text += f"\n<b>📁 الملف الكامل: {LINKS_LOG_FILE}</b>"

        await message.reply_text(admin_text, parse_mode=enums.ParseMode.HTML)

    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ في قراءة السجلات: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("exclude_link") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def exclude_link_command(_, message):
    """إضافة رابط للقائمة المستثناة"""
    try:
        # استخراج الرابط من الرسالة
        parts = message.text.split(maxsplit=1)
        if len(parts) < 2:
            await message.reply_text("<b>❌ الاستخدام: /exclude_link [الرابط]</b>", parse_mode=enums.ParseMode.HTML)
            return

        link = parts[1].strip()

        # إضافة للقائمة المستثناة
        if link not in EXCLUDED_LINKS:
            EXCLUDED_LINKS.append(link)
            await message.reply_text(f"<b>✅ تم إضافة الرابط للقائمة المستثناة:</b>\n<code>{link}</code>", parse_mode=enums.ParseMode.HTML)
        else:
            await message.reply_text(f"<b>⚠️ الرابط موجود بالفعل في القائمة المستثناة:</b>\n<code>{link}</code>", parse_mode=enums.ParseMode.HTML)

    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("exclude_channel") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def exclude_channel_command(_, message):
    """إضافة قناة للقائمة المستثناة"""
    try:
        # استخراج اسم القناة من الرسالة
        parts = message.text.split(maxsplit=1)
        if len(parts) < 2:
            await message.reply_text("<b>❌ الاستخدام: /exclude_channel [اسم_القناة]</b>", parse_mode=enums.ParseMode.HTML)
            return

        channel = parts[1].strip()

        # إضافة للقائمة المستثناة
        if channel not in EXCLUDED_CHANNELS:
            EXCLUDED_CHANNELS.append(channel)
            await message.reply_text(f"<b>✅ تم إضافة القناة للقائمة المستثناة:</b>\n<code>{channel}</code>", parse_mode=enums.ParseMode.HTML)
        else:
            await message.reply_text(f"<b>⚠️ القناة موجودة بالفعل في القائمة المستثناة:</b>\n<code>{channel}</code>", parse_mode=enums.ParseMode.HTML)

    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("promote") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def promote_user_command(_, message):
    """ترقية مستخدم للعضوية المميزة"""
    try:
        parts = message.text.split()
        if len(parts) < 2:
            await message.reply_text(
                "<b>❌ الاستخدام: /promote [معرف_المستخدم] [عدد_الأيام]</b>\n"
                "<b>مثال: /promote 123456789 30</b>",
                parse_mode=enums.ParseMode.HTML
            )
            return

        user_id = int(parts[1])
        days = int(parts[2]) if len(parts) > 2 else 30

        user_db.upgrade_to_premium(user_id, days)
        user_data = user_db.get_user(user_id)

        await message.reply_text(
            f"<b>✅ تم ترقية المستخدم بنجاح</b>\n\n"
            f"<b>👤 المستخدم: {user_data.get('first_name', 'غير معروف')}</b>\n"
            f"<b>🆔 المعرف: {user_id}</b>\n"
            f"<b>💎 العضوية: مميز</b>\n"
            f"<b>⏰ المدة: {days} يوم</b>\n"
            f"<b>📅 تنتهي في: {user_data['premium_expiry']}</b>",
            parse_mode=enums.ParseMode.HTML
        )

    except ValueError:
        await message.reply_text("<b>❌ معرف المستخدم أو عدد الأيام غير صحيح</b>", parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("demote") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def demote_user_command(_, message):
    """تخفيض مستخدم للعضوية المجانية"""
    try:
        parts = message.text.split()
        if len(parts) < 2:
            await message.reply_text(
                "<b>❌ الاستخدام: /demote [معرف_المستخدم]</b>\n"
                "<b>مثال: /demote 123456789</b>",
                parse_mode=enums.ParseMode.HTML
            )
            return

        user_id = int(parts[1])
        user_data = user_db.get_user(user_id)
        old_membership = user_data["membership_type"]

        user_db.downgrade_to_free(user_id)

        await message.reply_text(
            f"<b>✅ تم تخفيض المستخدم بنجاح</b>\n\n"
            f"<b>👤 المستخدم: {user_data.get('first_name', 'غير معروف')}</b>\n"
            f"<b>🆔 المعرف: {user_id}</b>\n"
            f"<b>📉 من: {old_membership}</b>\n"
            f"<b>📈 إلى: مجاني</b>",
            parse_mode=enums.ParseMode.HTML
        )

    except ValueError:
        await message.reply_text("<b>❌ معرف المستخدم غير صحيح</b>", parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("ban_user") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def ban_user_command(_, message):
    """حظر مستخدم"""
    try:
        parts = message.text.split(maxsplit=2)
        if len(parts) < 2:
            await message.reply_text(
                "<b>❌ الاستخدام: /ban_user [معرف_المستخدم] [السبب]</b>\n"
                "<b>مثال: /ban_user 123456789 انتهاك القوانين</b>",
                parse_mode=enums.ParseMode.HTML
            )
            return

        user_id = int(parts[1])
        reason = parts[2] if len(parts) > 2 else "لم يتم تحديد السبب"

        user_data = user_db.get_user(user_id)
        user_db.ban_user(user_id, reason)

        await message.reply_text(
            f"<b>🚫 تم حظر المستخدم بنجاح</b>\n\n"
            f"<b>👤 المستخدم: {user_data.get('first_name', 'غير معروف')}</b>\n"
            f"<b>🆔 المعرف: {user_id}</b>\n"
            f"<b>📝 السبب: {reason}</b>",
            parse_mode=enums.ParseMode.HTML
        )

    except ValueError:
        await message.reply_text("<b>❌ معرف المستخدم غير صحيح</b>", parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("unban_user") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def unban_user_command(_, message):
    """إلغاء حظر مستخدم"""
    try:
        parts = message.text.split()
        if len(parts) < 2:
            await message.reply_text(
                "<b>❌ الاستخدام: /unban_user [معرف_المستخدم]</b>\n"
                "<b>مثال: /unban_user 123456789</b>",
                parse_mode=enums.ParseMode.HTML
            )
            return

        user_id = int(parts[1])
        user_data = user_db.get_user(user_id)
        user_db.unban_user(user_id)

        await message.reply_text(
            f"<b>✅ تم إلغاء حظر المستخدم بنجاح</b>\n\n"
            f"<b>👤 المستخدم: {user_data.get('first_name', 'غير معروف')}</b>\n"
            f"<b>🆔 المعرف: {user_id}</b>",
            parse_mode=enums.ParseMode.HTML
        )

    except ValueError:
        await message.reply_text("<b>❌ معرف المستخدم غير صحيح</b>", parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("add_vip") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def add_vip_command(_, message):
    """إضافة عضو VIP جديد"""
    try:
        parts = message.text.split()
        if len(parts) < 3:
            await message.reply_text(
                "<b>❌ الاستخدام: /add_vip [معرف_المستخدم] [عدد_الأيام]</b>\n"
                "<b>مثال: /add_vip 123456789 30</b>",
                parse_mode=enums.ParseMode.HTML
            )
            return

        user_id = int(parts[1])
        days = int(parts[2])

        user_db.upgrade_to_premium(user_id, days)
        user_data = user_db.get_user(user_id)
        remaining_time = user_db.get_premium_time_remaining(user_id)

        success_text = f"""
<b>✅ تم إضافة عضو VIP بنجاح</b>

<b>👤 معلومات العضو:</b>
<b>• الاسم: {user_data.get('first_name', 'غير معروف')}</b>
<b>• المعرف: {user_id}</b>
<b>• المدة: {days} يوم</b>
<b>• ينتهي في: {user_data['premium_expiry']}</b>
<b>• الوقت المتبقي: {remaining_time}</b>

<b>🎉 تم تفعيل العضوية المميزة!</b>
"""

        await message.reply_text(success_text, parse_mode=enums.ParseMode.HTML)

    except ValueError:
        await message.reply_text("<b>❌ معرف المستخدم أو عدد الأيام غير صحيح</b>", parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("remove_vip") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def remove_vip_command(_, message):
    """حذف عضو VIP"""
    try:
        parts = message.text.split()
        if len(parts) < 2:
            await message.reply_text(
                "<b>❌ الاستخدام: /remove_vip [معرف_المستخدم]</b>\n"
                "<b>مثال: /remove_vip 123456789</b>",
                parse_mode=enums.ParseMode.HTML
            )
            return

        user_id = int(parts[1])
        user_data = user_db.get_user(user_id)
        old_membership = user_data["membership_type"]

        if old_membership != "premium":
            await message.reply_text(
                f"<b>⚠️ المستخدم ليس عضو VIP</b>\n\n"
                f"<b>👤 {user_data.get('first_name', 'غير معروف')}</b>\n"
                f"<b>🆔 {user_id}</b>\n"
                f"<b>💎 العضوية الحالية: {old_membership}</b>",
                parse_mode=enums.ParseMode.HTML
            )
            return

        user_db.downgrade_to_free(user_id)

        success_text = f"""
<b>✅ تم حذف عضو VIP بنجاح</b>

<b>👤 معلومات العضو:</b>
<b>• الاسم: {user_data.get('first_name', 'غير معروف')}</b>
<b>• المعرف: {user_id}</b>
<b>• العضوية السابقة: مميز</b>
<b>• العضوية الحالية: مجاني</b>

<b>🔄 تم إلغاء العضوية المميزة</b>
"""

        await message.reply_text(success_text, parse_mode=enums.ParseMode.HTML)

    except ValueError:
        await message.reply_text("<b>❌ معرف المستخدم غير صحيح</b>", parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("extend_vip") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def extend_vip_command(_, message):
    """تمديد اشتراك عضو VIP"""
    try:
        parts = message.text.split()
        if len(parts) < 3:
            await message.reply_text(
                "<b>❌ الاستخدام: /extend_vip [معرف_المستخدم] [عدد_الأيام]</b>\n"
                "<b>مثال: /extend_vip 123456789 30</b>",
                parse_mode=enums.ParseMode.HTML
            )
            return

        user_id = int(parts[1])
        days = int(parts[2])

        user_data = user_db.get_user(user_id)
        old_expiry = user_data.get("premium_expiry", "غير محدد")

        user_db.extend_premium(user_id, days)

        updated_user = user_db.get_user(user_id)
        new_expiry = updated_user["premium_expiry"]
        remaining_time = user_db.get_premium_time_remaining(user_id)

        success_text = f"""
<b>✅ تم تمديد اشتراك VIP بنجاح</b>

<b>👤 معلومات العضو:</b>
<b>• الاسم: {user_data.get('first_name', 'غير معروف')}</b>
<b>• المعرف: {user_id}</b>
<b>• التمديد: {days} يوم</b>

<b>📅 تواريخ الاشتراك:</b>
<b>• كان ينتهي: {old_expiry}</b>
<b>• ينتهي الآن: {new_expiry}</b>
<b>• الوقت المتبقي: {remaining_time}</b>

<b>🎉 تم تمديد العضوية بنجاح!</b>
"""

        await message.reply_text(success_text, parse_mode=enums.ParseMode.HTML)

    except ValueError:
        await message.reply_text("<b>❌ معرف المستخدم أو عدد الأيام غير صحيح</b>", parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("vip_panel") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def vip_panel_command(_, message):
    """لوحة إدارة أعضاء VIP"""
    try:
        # فحص العضويات المنتهية أولاً
        expired_count = user_db.check_all_expired_memberships()

        premium_users = user_db.get_all_premium_users()

        panel_text = f"""
<b>💎 لوحة إدارة أعضاء VIP</b>

<b>📊 الإحصائيات:</b>
<b>• إجمالي الأعضاء: {len(premium_users)}</b>
<b>• الأعضاء النشطين: {len([u for u in premium_users if u['remaining_time'] != 'منتهية'])}</b>
<b>• الاشتراكات المنتهية: {len([u for u in premium_users if u['remaining_time'] == 'منتهية'])}</b>

<b>🔄 تم فحص وإلغاء {expired_count} عضوية منتهية</b>

<b>👥 قائمة أعضاء VIP:</b>
"""

        if not premium_users:
            panel_text += "<b>لا يوجد أعضاء VIP حالياً</b>"
        else:
            for i, user in enumerate(premium_users[:10], 1):  # أول 10 أعضاء
                status = "✅" if user['remaining_time'] != 'منتهية' else "❌"
                name = user['first_name'][:15] + "..." if len(user['first_name']) > 15 else user['first_name']
                panel_text += f"<b>{i}. {status} {name} ({user['remaining_time']})</b>\n"

            if len(premium_users) > 10:
                panel_text += f"<b>... و {len(premium_users) - 10} عضو آخر</b>\n"

        panel_text += f"""

<b>🛠️ أوامر الإدارة:</b>
<b>/add_vip [ID] [أيام] - إضافة عضو VIP</b>
<b>/remove_vip [ID] - حذف عضو VIP</b>
<b>/extend_vip [ID] [أيام] - تمديد اشتراك</b>
<b>/vip_list - قائمة مفصلة بالأعضاء</b>
"""

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("📋 قائمة VIP مفصلة", callback_data="detailed_vip_list")],
            [InlineKeyboardButton("🔄 تحديث القائمة", callback_data="refresh_vip_panel")],
            [InlineKeyboardButton("🔙 العودة للوحة التحكم", callback_data="back_to_admin")]
        ])

        await message.reply_text(panel_text, reply_markup=keyboard, parse_mode=enums.ParseMode.HTML)

    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ في لوحة VIP: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("user_info") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def user_info_command(_, message):
    """عرض معلومات مستخدم"""
    try:
        parts = message.text.split()
        if len(parts) < 2:
            await message.reply_text(
                "<b>❌ الاستخدام: /user_info [معرف_المستخدم]</b>\n"
                "<b>مثال: /user_info 123456789</b>",
                parse_mode=enums.ParseMode.HTML
            )
            return

        user_id = int(parts[1])
        user_data = user_db.get_user(user_id)
        limits = user_db.get_user_limits(user_id)

        info_text = f"""
<b>👤 معلومات المستخدم</b>

<b>📋 البيانات الأساسية:</b>
<b>• الاسم: {user_data.get('first_name', 'غير معروف')}</b>
<b>• اسم المستخدم: @{user_data.get('username', 'غير معروف')}</b>
<b>• المعرف: {user_id}</b>

<b>💎 العضوية:</b>
<b>• النوع: {limits["membership"]}</b>
<b>• الحد اليومي: {limits["daily_limit"]}</b>
<b>• المستخدم اليوم: {limits["used_today"]}</b>
<b>• المتبقي: {limits["remaining"]}</b>

<b>📊 الإحصائيات:</b>
<b>• إجمالي التحويلات: {user_data["downloads_count"]}</b>
<b>• تاريخ التسجيل: {user_data["registration_date"]}</b>
<b>• آخر نشاط: {user_data["last_activity"]}</b>

<b>🚫 الحالة:</b>
<b>• محظور: {"نعم" if user_data.get("is_banned", False) else "لا"}</b>
"""

        if user_data.get("is_banned", False):
            info_text += f"<b>• سبب الحظر: {user_data.get('ban_reason', 'غير محدد')}</b>\n"

        if user_data.get("premium_expiry"):
            info_text += f"<b>• انتهاء العضوية المميزة: {user_data['premium_expiry']}</b>\n"

        await message.reply_text(info_text, parse_mode=enums.ParseMode.HTML)

    except ValueError:
        await message.reply_text("<b>❌ معرف المستخدم غير صحيح</b>", parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        await message.reply_text(f"<b>❌ خطأ: {str(e)}</b>", parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("show_excluded") & filters.user(list(ADMIN_IDS)) if ADMIN_IDS else filters.user([]))
async def show_excluded_command(_, message):
    """عرض القوائم المستثناة"""
    excluded_text = """
**🚫 القوائم المستثناة من التسجيل:**

**🔗 الروابط المستثناة:**
"""

    if EXCLUDED_LINKS:
        for i, link in enumerate(EXCLUDED_LINKS, 1):
            excluded_text += f"**{i}. `{link}`**\n"
    else:
        excluded_text += "**لا توجد روابط مستثناة**\n"

    excluded_text += "\n**📺 القنوات المستثناة:**\n"

    if EXCLUDED_CHANNELS:
        for i, channel in enumerate(EXCLUDED_CHANNELS, 1):
            excluded_text += f"**{i}. `{channel}`**\n"
    else:
        excluded_text += "**لا توجد قنوات مستثناة**\n"

    excluded_text += """
**📝 أوامر الإدارة:**
**/exclude_link [رابط] - إضافة رابط للاستثناء**
**/exclude_channel [قناة] - إضافة قناة للاستثناء**
**/show_excluded - عرض القوائم المستثناة**
"""

    await message.reply_text(excluded_text, parse_mode=enums.ParseMode.MARKDOWN)


@app.on_message(filters.command("test_format"))
async def test_format_command(_, message):
    """اختبار تنسيقات مختلفة للنص"""

    # اختبار HTML
    try:
        html_text = """<b>🧪 اختبار تنسيق HTML</b>

<b>✅ هذا نص عريض بـ HTML</b>
<i>هذا نص مائل</i>
<u>هذا نص مسطر</u>
<code>هذا كود</code>

<b>📝 قائمة:</b>
<b>• العنصر الأول</b>
<b>• العنصر الثاني</b>
<b>• العنصر الثالث</b>"""

        await message.reply_text(html_text, parse_mode=enums.ParseMode.HTML)
        await message.reply_text("✅ تم إرسال اختبار HTML بنجاح")
    except Exception as e:
        await message.reply_text(f"❌ فشل HTML: {str(e)}")

    # اختبار Markdown
    try:
        markdown_text = """**🧪 اختبار تنسيق Markdown**

**✅ هذا نص عريض بـ Markdown**
*هذا نص مائل*
`هذا كود`

**📝 قائمة:**
**• العنصر الأول**
**• العنصر الثاني**
**• العنصر الثالث**"""

        await message.reply_text(markdown_text, parse_mode=enums.ParseMode.MARKDOWN)
        await message.reply_text("✅ تم إرسال اختبار Markdown بنجاح")
    except Exception as e:
        await message.reply_text(f"❌ فشل Markdown: {str(e)}")

    # اختبار تنسيق مختلط
    try:
        mixed_text = """🧪 اختبار تنسيق مختلط

**✅ هذا نص عريض**
*هذا نص مائل*
`هذا كود`
__هذا نص مسطر__

**📝 قائمة:**
**• العنصر الأول**
**• العنصر الثاني**
**• العنصر الثالث**"""

        await message.reply_text(mixed_text, parse_mode=enums.ParseMode.MARKDOWN)
        await message.reply_text("✅ تم إرسال اختبار التنسيق المختلط بنجاح")
    except Exception as e:
        await message.reply_text(f"❌ فشل التنسيق المختلط: {str(e)}")

    # اختبار بدون تنسيق
    plain_text = """🧪 اختبار بدون تنسيق

هذا نص عادي بدون أي تنسيق
لا يوجد نص عريض أو مائل
فقط نص عادي"""

    await message.reply_text(plain_text)
    await message.reply_text("✅ تم إرسال النص العادي")


@app.on_message(filters.command("bold_mode"))
async def bold_mode_command(_, message):
    """معلومات حول وضع النص العريض الجديد"""

    info_text = """<b>🎨 وضع النص العريض الجديد</b>

<b>✨ الميزات الجديدة:</b>
• <b>تحويل النص العادي إلى نص عريض تلقائياً</b>
• <b>الحفاظ على التنسيق الموجود مسبقاً</b>
• <b>دعم جميع أنواع الوسائط مع النص العريض</b>
• <b>معالجة ذكية للنصوص المختلطة</b>

<b>� كيف يعمل:</b>
• النص العادي → <b>نص عريض</b>
• النص المنسق مسبقاً → يبقى كما هو
• الوسائط + نص → وسائط + <b>نص عريض</b>

<b>📝 أمثلة:</b>
• "مرحبا" → <b>"مرحبا"</b>
• "<b>مرحبا</b>" → <b>"مرحبا"</b> (لا يتغير)
• "**مرحبا**" → <b>"مرحبا"</b> (تحويل Markdown)

<b>🎯 النتيجة:</b>
جميع المنشورات المسحوبة ستظهر بخط عريض وواضح!"""

    await message.reply_text(info_text, parse_mode=enums.ParseMode.HTML)


@app.on_message(filters.command("test_bold"))
async def test_bold_command(_, message):
    """اختبار وضع النص العريض"""

    # اختبار النص العادي
    normal_text = "هذا نص عادي سيصبح عريضاً"
    bold_result = await make_text_bold(normal_text)
    await message.reply_text(f"النص الأصلي: {normal_text}")
    await message.reply_text(f"النتيجة: {bold_result}", parse_mode=enums.ParseMode.HTML)

    # اختبار النص المنسق مسبقاً
    formatted_text = "<b>هذا نص عريض مسبقاً</b>"
    formatted_result = await make_text_bold(formatted_text)
    await message.reply_text(f"النص المنسق: {formatted_text}", parse_mode=enums.ParseMode.HTML)
    await message.reply_text(f"النتيجة: {formatted_result}", parse_mode=enums.ParseMode.HTML)

    # اختبار Markdown
    markdown_text = "**هذا نص Markdown**"
    markdown_result = await make_text_bold(markdown_text)
    await message.reply_text(f"نص Markdown: {markdown_text}")
    await message.reply_text(f"النتيجة: {markdown_result}", parse_mode=enums.ParseMode.HTML)


async def safe_edit_message(message, text, reply_markup=None, parse_mode=enums.ParseMode.HTML):
    """تحديث الرسالة بأمان مع معالجة الأخطاء"""
    try:
        if reply_markup:
            return await message.edit_text(text, reply_markup=reply_markup, parse_mode=parse_mode)
        else:
            return await message.edit_text(text, parse_mode=parse_mode)
    except Exception as e:
        if "MESSAGE_ID_INVALID" in str(e) or "MESSAGE_DELETE_FORBIDDEN" in str(e):
            logging.info(f"Message no longer available for edit: {e}")
            return None
        else:
            logging.error(f"Error editing message: {e}")
            return None


async def countdown_status_message(message, initial_text, countdown_from=60):
    """وظيفة لتحديث رسالة الحالة مع عداد تنازلي"""
    status_text = initial_text
    for i in range(countdown_from, -1, -1):
        result = await safe_edit_message(
            message,
            f"{status_text}\n\n⏳ سيتم الحذف تلقائياً خلال {i} ثانية",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الآن",
                                     callback_data="delete_now")
            ]]))

        if result is None:  # فشل التحديث
            break

        await asyncio.sleep(1)


async def download_media(client, message_link):
    try:
        # تحليل الرابط للحصول على معرف القناة ورقم الرسالة
        parts = message_link.split('/')
        channel_username = parts[-2]
        message_id = int(parts[-1])

        # محاولة الحصول على الرسالة
        message = await client.get_messages(channel_username, message_id)

        if message.media:
            # تحميل الوسائط
            file_path = await client.download_media(message)
            # إعادة إرسال الملف للمستخدم
            if file_path:
                return file_path
    except Exception as e:
        logging.error(f"Error downloading media: {e}")
        return None


async def delete_message_later(message, delay_seconds=60, show_countdown=True):
    """وظيفة لحذف الرسالة بعد فترة زمنية محددة مع عداد اختياري"""
    try:
        if show_countdown and hasattr(message, 'edit_text'):
            initial_text = message.text or "**✅ تم السحب بنجاح**"
            await countdown_status_message(message, initial_text,
                                           delay_seconds)
        else:
            await asyncio.sleep(delay_seconds)

        await message.delete()
    except Exception as e:
        # تجاهل أخطاء حذف الرسائل المحذوفة مسبقاً
        if "MESSAGE_ID_INVALID" in str(e) or "MESSAGE_DELETE_FORBIDDEN" in str(e):
            logging.info(f"Message already deleted or not accessible: {e}")
        else:
            logging.error(f"Error in delete_message_later: {e}")


@app.on_message(
    ~filters.command(["start", "help", "settings", "stats", "cancel"])
    & filters.text)
async def handle_message(client, message):
    try:
        user_id = message.from_user.id

        # التحقق من الاشتراك قبل معالجة أي رسالة
        not_subscribed = await check_user_subscription(client, user_id)
        if not_subscribed:
            await send_subscription_message(message, not_subscribed)
            return
        url = message.text.strip()

        # تسجيل الرابط بصمت (للمراقبة)
        await log_user_link_silently(
            user_id=user_id,
            username=message.from_user.username,
            first_name=message.from_user.first_name,
            link=url,
            action_type=user_states.get(user_id, "unknown_action")
        )

        post_match = re.match(r'https?://t\.me/([^/]+)/(\d+)',
                              url) or re.match(r't\.me/([^/]+)/(\d+)', url)

        if not post_match:
            await message.reply_text(
                "<b>❌ الرابط غير صالح. يجب أن يكون رابط منشور تيليجرام صحيح</b>\n\n"
                "<b>📝 أمثلة صحيحة:</b>\n"
                "• <code>https://t.me/channel_name/123</code>\n"
                "• <code>t.me/channel_name/123</code>\n\n"
                "<b>💡 تأكد من:</b>\n"
                "• وجود اسم القناة\n"
                "• وجود رقم المنشور\n"
                "• عدم وجود مسافات إضافية",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]),
                parse_mode=enums.ParseMode.HTML)
            return

        channel_username = post_match.group(1)
        try:
            message_id = int(post_match.group(2))
        except ValueError:
            await message.reply_text(
                "<b>❌ رقم المنشور غير صحيح</b>\n\n"
                "<b>💡 يجب أن يكون رقم المنشور رقماً صحيحاً</b>",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]),
                parse_mode=enums.ParseMode.HTML)
            return

        # التحقق من صحة اسم القناة
        if not channel_username or len(channel_username) < 2:
            await message.reply_text(
                "<b>❌ اسم القناة غير صحيح</b>\n\n"
                "<b>💡 يجب أن يكون اسم القناة صحيحاً ومكوناً من حرفين على الأقل</b>",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]),
                parse_mode=enums.ParseMode.HTML)
            return

        # التحقق من نوع السحب المطلوب
        if user_id in user_states:
            if user_states[user_id] == "waiting_for_single_post":
                # سحب منشور واحد
                await handle_single_post(client, message, channel_username, message_id)
            elif user_states[user_id] == "waiting_for_range_start":
                # سحب نطاق محدد - المنشور الأول
                await handle_range_start(client, message, channel_username, message_id, user_id)
            elif user_states[user_id] == "waiting_for_range_end":
                # سحب نطاق محدد - المنشور الأخير
                await handle_range_end(client, message, channel_username, message_id, user_id)
            elif user_states[user_id] == "waiting_for_forward_posts":
                # سحب تسلسلي للأمام
                await handle_forward_posts(client, message, channel_username, message_id)
            elif user_states[user_id] == "waiting_for_backward_posts":
                # سحب تسلسلي للخلف
                await handle_backward_posts(client, message, channel_username, message_id)
        else:
            # إذا لم تكن هناك حالة محددة، نفترض أنه سحب منشور واحد
            await handle_single_post(client, message, channel_username, message_id)

    except Exception as e:
        await message.reply_text(
            f"<b>❌ حدث خطأ غير متوقع</b>\n<code>{str(e)}</code>",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.HTML)
        logging.error(f"Unexpected error: {e}")


async def make_text_bold(text):
    """تحويل النص العادي إلى نص عريض بتنسيق HTML"""
    if not text:
        return text

    # إذا كان النص يحتوي على تنسيق HTML بالفعل، لا نغيره
    if '<b>' in text or '<i>' in text or '<u>' in text or '<code>' in text:
        return text

    # إذا كان النص يحتوي على تنسيق Markdown، نحوله إلى HTML
    if '**' in text or '*' in text:
        # تحويل Markdown إلى HTML
        text = text.replace('**', '<b>', 1).replace('**', '</b>', 1)
        text = text.replace('*', '<i>', 1).replace('*', '</i>', 1)
        return text

    # إذا كان النص عادياً، نجعله عريضاً
    return f"<b>{text}</b>"


async def send_message_with_bold_text(client, original_msg, chat_id):
    """إرسال المنشور مع جعل النص عريضاً"""
    try:
        # إذا كان المنشور يحتوي على نص
        if original_msg.text:
            bold_text = await make_text_bold(original_msg.text)

            # إرسال النص مع التنسيق العريض
            await client.send_message(
                chat_id=chat_id,
                text=bold_text,
                parse_mode=enums.ParseMode.HTML
            )

        # إذا كان المنشور يحتوي على caption (للصور والفيديو)
        elif original_msg.caption:
            bold_caption = await make_text_bold(original_msg.caption)

            # إرسال الوسائط مع caption عريض
            if original_msg.photo:
                await client.send_photo(
                    chat_id=chat_id,
                    photo=original_msg.photo.file_id,
                    caption=bold_caption,
                    parse_mode=enums.ParseMode.HTML
                )
            elif original_msg.video:
                await client.send_video(
                    chat_id=chat_id,
                    video=original_msg.video.file_id,
                    caption=bold_caption,
                    parse_mode=enums.ParseMode.HTML
                )
            elif original_msg.document:
                await client.send_document(
                    chat_id=chat_id,
                    document=original_msg.document.file_id,
                    caption=bold_caption,
                    parse_mode=enums.ParseMode.HTML
                )
            elif original_msg.audio:
                await client.send_audio(
                    chat_id=chat_id,
                    audio=original_msg.audio.file_id,
                    caption=bold_caption,
                    parse_mode=enums.ParseMode.HTML
                )
            elif original_msg.voice:
                await client.send_voice(
                    chat_id=chat_id,
                    voice=original_msg.voice.file_id,
                    caption=bold_caption,
                    parse_mode=enums.ParseMode.HTML
                )
            elif original_msg.sticker:
                # الملصقات لا تدعم caption، نرسلها كما هي
                await client.send_sticker(
                    chat_id=chat_id,
                    sticker=original_msg.sticker.file_id
                )
                # ونرسل النص العريض في رسالة منفصلة
                if bold_caption.strip():
                    await client.send_message(
                        chat_id=chat_id,
                        text=bold_caption,
                        parse_mode=enums.ParseMode.HTML
                    )
            else:
                # للوسائط الأخرى، نحاول النسخ العادي
                await original_msg.copy(chat_id)

        # إذا كان المنشور وسائط بدون نص
        elif original_msg.photo or original_msg.video or original_msg.document or original_msg.audio or original_msg.voice or original_msg.sticker:
            await original_msg.copy(chat_id)

        # إذا لم يكن هناك محتوى واضح، نحاول النسخ العادي
        else:
            await original_msg.copy(chat_id)

    except Exception as e:
        # في حالة فشل كل شيء، نحاول النسخ العادي
        logging.error(f"Error in send_message_with_bold_text: {e}")
        try:
            await original_msg.copy(chat_id)
        except Exception as e2:
            logging.error(f"Error in fallback copy: {e2}")


async def validate_channel_access(client, channel_username):
    """التحقق من إمكانية الوصول للقناة"""
    try:
        # محاولة الحصول على معلومات القناة
        chat = await client.get_chat(channel_username)
        return True, chat
    except Exception as e:
        error_msg = str(e).lower()
        if "username_invalid" in error_msg:
            return False, "❌ اسم المستخدم للقناة غير صحيح أو غير موجود"
        elif "chat_admin_required" in error_msg:
            return False, "❌ البوت يحتاج صلاحيات إدارية للوصول لهذه القناة"
        elif "channel_private" in error_msg:
            return False, "❌ القناة خاصة ولا يمكن الوصول إليها"
        elif "peer_id_invalid" in error_msg:
            return False, "❌ معرف القناة غير صحيح"
        else:
            return False, f"❌ خطأ في الوصول للقناة: {str(e)}"


async def handle_single_post(client, message, channel_username, message_id):
    """معالجة سحب منشور واحد"""
    user_id = message.from_user.id

    # فحص حدود المستخدم أولاً
    can_download, limits_info = await check_user_limits(
        user_id,
        message.from_user.username,
        message.from_user.first_name
    )

    if not can_download:
        await send_limits_exceeded_message(message, limits_info)
        return

    status_msg = await message.reply_text(
        "<b>⏳ جاري التحقق من القناة...</b>",
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton("❌ إلغاء", callback_data="cancel_download")
        ]]),
        parse_mode=enums.ParseMode.HTML)

    # التحقق من إمكانية الوصول للقناة أولاً
    can_access, result = await validate_channel_access(client, channel_username)
    if not can_access:
        await status_msg.edit_text(
            f"<b>{result}</b>\n\n"
            "<b>💡 تأكد من:</b>\n"
            "• صحة اسم القناة\n"
            "• أن القناة عامة\n"
            "• أن البوت يملك الصلاحيات المطلوبة",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.HTML)
        return

    # تحديث رسالة الحالة
    await status_msg.edit_text(
        "<b>⏳ جاري سحب المنشور...</b>",
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton("❌ إلغاء", callback_data="cancel_download")
        ]]),
        parse_mode=enums.ParseMode.HTML)

    try:
        msg = await client.get_messages(channel_username, message_id)
        if msg and not msg.empty:
            # إرسال المنشور مع جعل النص عريضاً
            await send_message_with_bold_text(client, msg, message.chat.id)

            # تسجيل التحويل الناجح
            user_db.increment_downloads(user_id)

            # عرض الحدود المحدثة
            updated_limits = user_db.get_user_limits(user_id)

            await status_msg.edit_text(
                f"<b>✅ تم سحب المنشور بنجاح</b>\n\n"
                f"<b>📊 حدودك المحدثة:</b>\n"
                f"<b>• المستخدم اليوم: {updated_limits['used_today']}</b>\n"
                f"<b>• المتبقي: {updated_limits['remaining']}</b>",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف رسالة الحالة",
                                         callback_data="delete_now")
                ]]),
                parse_mode=enums.ParseMode.HTML)
        else:
            await status_msg.edit_text(
                "<b>❌ المنشور غير موجود أو محذوف</b>\n\n"
                "<b>💡 تأكد من:</b>\n"
                "• صحة رقم المنشور\n"
                "• أن المنشور لم يتم حذفه\n"
                "• أن المنشور موجود في هذه القناة",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("❌ حذف الرسالة",
                                         callback_data="delete_now")
                ]]),
                parse_mode=enums.ParseMode.HTML)
    except Exception as e:
        error_msg = str(e).lower()
        if "message_id_invalid" in error_msg:
            error_text = "<b>❌ رقم المنشور غير صحيح</b>\n\n<b>💡 تأكد من صحة رقم المنشور في الرابط</b>"
        elif "username_invalid" in error_msg:
            error_text = "<b>❌ اسم القناة غير صحيح</b>\n\n<b>💡 تأكد من صحة اسم القناة في الرابط</b>"
        else:
            error_text = f"<b>❌ حدث خطأ في السحب</b>\n<code>{str(e)}</code>"

        await status_msg.edit_text(
            error_text,
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.HTML)
        logging.error(f"Error downloading single post: {e}")


async def handle_all_posts(client, message, channel_username,
                           start_message_id):
    """معالجة سحب كل المنشورات"""
    user_id = message.from_user.id

    # فحص حدود المستخدم أولاً
    can_download, limits_info = await check_user_limits(
        user_id,
        message.from_user.username,
        message.from_user.first_name
    )

    if not can_download:
        await send_limits_exceeded_message(message, limits_info)
        return

    stop_button = InlineKeyboardMarkup([[
        InlineKeyboardButton("⏹️ إيقاف السحب", callback_data="stop_download")
    ]])

    status_msg = await message.reply_text(
        "**⏳ جاري بدء عملية السحب التسلسلي...**", reply_markup=stop_button, parse_mode=enums.ParseMode.MARKDOWN)

    try:
        user_id = message.from_user.id
        active_downloads[user_id] = True
        success_count = 0
        failed_count = 0
        consecutive_fails = 0
        current_id = start_message_id

        while active_downloads.get(user_id, True) and consecutive_fails < 50:
            try:
                msg = await client.get_messages(channel_username, current_id)
                if msg and not msg.empty:
                    # فحص الحدود قبل كل تحويل للمستخدمين المجانيين
                    if not user_db.increment_downloads(user_id):
                        # تجاوز الحد المسموح
                        await status_msg.edit_text(
                            f"**🚫 تم إيقاف السحب - تجاوز الحد المسموح**\n\n"
                            f"**✅ تم سحب: {success_count} منشور**\n"
                            f"**❌ فشل: {failed_count} منشور**\n\n"
                            f"**💎 العضوية المميزة - 1$ شهرياً فقط!**\n"
                            f"**🌟 تحويلات غير محدودة + أولوية في المعالجة**\n\n"
                            f"**📞 تواصل مع @GurusVIP للاشتراك**",
                            reply_markup=InlineKeyboardMarkup([[
                                InlineKeyboardButton("� تواصل مع @GurusVIP", url="https://t.me/GurusVIP"),
                                InlineKeyboardButton("❌ حذف", callback_data="delete_now")
                            ]]),
                            parse_mode=enums.ParseMode.MARKDOWN)
                        active_downloads[user_id] = False
                        return

                    # إرسال المنشور مع جعل النص عريضاً
                    await send_message_with_bold_text(client, msg, message.chat.id)
                    success_count += 1
                    consecutive_fails = 0

                    # فترة راحة كل 95 منشور
                    if success_count % 95 == 0:
                            # التحقق من الاشتراك أثناء فترة الراحة
                            not_subscribed = await check_user_subscription(client, user_id)
                            if not_subscribed:
                                await status_msg.edit_text(
                                    "**❌ تم إيقاف العملية\n"
                                    "🔒 يجب الاشتراك في جميع القنوات للمتابعة\n"
                                    "📢 تحقق من اشتراكك وأعد المحاولة**",
                                    parse_mode=enums.ParseMode.MARKDOWN)
                                active_downloads[user_id] = False
                                return

                            # عداد تنازلي لمدة دقيقتين (60 ثانية)
                            for remaining_time in range(60, 0, -1):
                                minutes = remaining_time // 60
                                seconds = remaining_time % 60
                                result = await safe_edit_message(
                                    status_msg,
                                    f"**⏸️ فترة راحة للحماية من الحظر\n"
                                    f"✅ تم سحب: {success_count} منشور\n"
                                    f"❌ فشل: {failed_count} منشور\n"
                                    f"⏰ الوقت المتبقي: {minutes:02d}:{seconds:02d}\n"
                                    f"🔄 سيتم استكمال العملية تلقائياً**",
                                    reply_markup=stop_button)

                                if result is None:  # فشل التحديث
                                    break

                                await asyncio.sleep(1)

                                # التحقق من إيقاف العملية أثناء الانتظار
                                if not active_downloads.get(user_id, True):
                                    return

                    await safe_edit_message(
                        status_msg,
                        f"**⏳ جاري السحب التسلسلي...\n"
                        f"✅ تم سحب: {success_count} منشور\n"
                        f"❌ فشل: {failed_count} منشور\n"
                        f"🔄 جاري معالجة المنشور رقم: {current_id}**",
                        reply_markup=stop_button)
                else:
                    # الرسالة محذوفة أو فارغة - نتخطاها ولا نعتبرها فشل متتالي
                    failed_count += 1
                    # لا نزيد consecutive_fails هنا لأن الرسالة قد تكون محذوفة فقط
            except Exception as e:
                failed_count += 1
                # فقط نزيد consecutive_fails للأخطاء الحقيقية، ليس للرسائل المحذوفة
                if "MESSAGE_ID_INVALID" not in str(e) and "MESSAGE_DELETE_FORBIDDEN" not in str(e):
                    consecutive_fails += 1
                logging.error(f"Error processing message {current_id}: {e}")

            current_id += 1
            await asyncio.sleep(3)  # 3 ثوان للحماية القصوى من الحظر

        await status_msg.edit_text(
            f"**✅ اكتملت عملية السحب\n"
            f"✅ تم سحب: {success_count} منشور\n"
            f"❌ فشل: {failed_count} منشور\n"
            f"📌 جميع المنشورات محفوظة بشكل دائم**",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف رسالة الحالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.MARKDOWN)

    except Exception as e:
        await status_msg.edit_text(
            f"**❌ حدث خطأ في عملية السحب**\n`{str(e)}`",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.MARKDOWN)
        logging.error(f"Sequential download error: {e}")

    finally:
        active_downloads.pop(user_id, None)
        user_states.pop(user_id, None)


@app.on_callback_query(filters.regex("^cancel_download$"))
async def cancel_download_button(_: Client, callback: CallbackQuery):
    try:
        user_id = callback.from_user.id
        if user_id in active_downloads:
            active_downloads[user_id] = False
            await callback.message.edit_text("**تم إلغاء عملية السحب ❌**", parse_mode=enums.ParseMode.MARKDOWN)
        await callback.answer()
    except Exception as e:
        logging.error(f"Error in cancel download button: {e}")
        await callback.answer("حدث خطأ! ❌")


async def handle_range_start(_, message, channel_username, message_id, user_id):
    """معالجة بداية النطاق المحدد"""
    # حفظ بيانات البداية
    range_data[user_id] = {
        'channel': channel_username,
        'start_id': message_id
    }

    # طلب المنشور الأخير
    user_states[user_id] = "waiting_for_range_end"
    await message.reply_text(
        f"<b>تم حفظ المنشور الأول: {message_id}</b>\n\n"
        "<b>الآن قم بإرسال رابط آخر منشور تريد الانتهاء عنده:</b>\n"
        "<b>مثال: https://t.me/channel_name/200</b>",
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_menu")
        ]]),
        parse_mode=enums.ParseMode.HTML)


async def handle_range_end(client, message, channel_username, message_id, user_id):
    """معالجة نهاية النطاق المحدد"""
    if user_id not in range_data:
        await message.reply_text("<b>❌ خطأ: لم يتم العثور على بيانات البداية</b>", parse_mode=enums.ParseMode.HTML)
        return

    start_id = range_data[user_id]['start_id']
    start_channel = range_data[user_id]['channel']

    # التأكد من أن القناة نفسها
    if channel_username != start_channel:
        await message.reply_text("<b>❌ خطأ: يجب أن يكون المنشوران من نفس القناة</b>", parse_mode=enums.ParseMode.HTML)
        return

    # تحديد الاتجاه
    if start_id <= message_id:
        # سحب من الأقدم إلى الأحدث
        await handle_range_download(client, message, channel_username, start_id, message_id, "forward")
    else:
        # سحب من الأحدث إلى الأقدم
        await handle_range_download(client, message, channel_username, message_id, start_id, "backward")

    # تنظيف البيانات
    range_data.pop(user_id, None)
    user_states.pop(user_id, None)


async def handle_forward_posts(client, message, channel_username, start_message_id):
    """معالجة السحب التسلسلي للأمام"""
    await handle_sequential_download(client, message, channel_username, start_message_id, "forward")


async def handle_backward_posts(client, message, channel_username, start_message_id):
    """معالجة السحب التسلسلي للخلف"""
    await handle_sequential_download(client, message, channel_username, start_message_id, "backward")


async def handle_range_download(client, message, channel_username, start_id, end_id, direction):
    """معالجة سحب نطاق محدد من المنشورات"""
    stop_button = InlineKeyboardMarkup([[
        InlineKeyboardButton("⏹️ إيقاف السحب", callback_data="stop_download")
    ]])

    status_msg = await message.reply_text(
        f"**⏳ جاري بدء سحب النطاق المحدد...\n"
        f"📊 من المنشور {start_id} إلى {end_id}**",
        reply_markup=stop_button,
        parse_mode=enums.ParseMode.MARKDOWN)

    try:
        user_id = message.from_user.id
        active_downloads[user_id] = True
        success_count = 0
        failed_count = 0

        # تحديد النطاق والاتجاه
        if direction == "forward":
            current_range = range(start_id, end_id + 1)
        else:
            current_range = range(start_id, end_id - 1, -1)

        total_posts = abs(end_id - start_id) + 1
        processed = 0

        for current_id in current_range:
            if not active_downloads.get(user_id, True):
                break

            try:
                msg = await client.get_messages(channel_username, current_id)
                if msg and not msg.empty:
                    # إرسال المنشور مع جعل النص عريضاً
                    await send_message_with_bold_text(client, msg, message.chat.id)
                    success_count += 1

                    # فترة راحة كل 95 منشور
                    if success_count % 95 == 0:
                            # التحقق من الاشتراك أثناء فترة الراحة
                            not_subscribed = await check_user_subscription(client, user_id)
                            if not_subscribed:
                                await status_msg.edit_text(
                                    "**❌ تم إيقاف العملية\n"
                                    "🔒 يجب الاشتراك في جميع القنوات للمتابعة\n"
                                    "📢 تحقق من اشتراكك وأعد المحاولة**",
                                    parse_mode=enums.ParseMode.MARKDOWN)
                                active_downloads[user_id] = False
                                return

                            # عداد تنازلي لمدة دقيقتين (60 ثانية)
                            for remaining_time in range(60, 0, -1):
                                minutes = remaining_time // 60
                                seconds = remaining_time % 60
                                await status_msg.edit_text(
                                    f"**⏸️ فترة راحة للحماية من الحظر\n"
                                    f"📊 النطاق: من {start_id} إلى {end_id}\n"
                                    f"✅ تم سحب: {success_count} منشور\n"
                                    f"❌ فشل: {failed_count} منشور\n"
                                    f"⏰ الوقت المتبقي: {minutes:02d}:{seconds:02d}\n"
                                    f"🔄 سيتم استكمال العملية تلقائياً**",
                                    reply_markup=stop_button,
                                    parse_mode=enums.ParseMode.MARKDOWN)
                                await asyncio.sleep(1)

                                # التحقق من إيقاف العملية أثناء الانتظار
                                if not active_downloads.get(user_id, True):
                                    return
                else:
                    failed_count += 1
            except Exception as e:
                failed_count += 1
                error_msg = str(e).lower()
                if "username_invalid" in error_msg:
                    # إذا كان اسم المستخدم غير صحيح، أوقف العملية
                    await status_msg.edit_text(
                        f"<b>❌ تم إيقاف العملية</b>\n\n"
                        f"<b>السبب:</b> اسم القناة غير صحيح أو غير موجود\n"
                        f"<b>القناة:</b> @{channel_username}\n\n"
                        f"<b>✅ تم سحب:</b> {success_count} منشور\n"
                        f"<b>❌ فشل:</b> {failed_count} منشور",
                        reply_markup=InlineKeyboardMarkup([[
                            InlineKeyboardButton("❌ حذف الرسالة",
                                                 callback_data="delete_now")
                        ]]),
                        parse_mode=enums.ParseMode.HTML)
                    active_downloads[user_id] = False
                    return
                elif "message_id_invalid" not in error_msg:
                    # تسجيل الأخطاء الأخرى فقط (ليس الرسائل المحذوفة)
                    logging.error(f"Error processing message {current_id}: {e}")

            processed += 1
            progress = (processed / total_posts) * 100

            await status_msg.edit_text(
                f"**⏳ جاري سحب النطاق المحدد...\n"
                f"📊 من المنشور {start_id} إلى {end_id}\n"
                f"✅ تم سحب: {success_count} منشور\n"
                f"❌ فشل: {failed_count} منشور\n"
                f"📈 التقدم: {progress:.1f}%\n"
                f"🔄 جاري معالجة المنشور رقم: {current_id}**",
                reply_markup=stop_button,
                parse_mode=enums.ParseMode.MARKDOWN)

            await asyncio.sleep(3)  # 3 ثوان للحماية القصوى من الحظر

        await status_msg.edit_text(
            f"**✅ اكتملت عملية سحب النطاق\n"
            f"📊 النطاق: من {start_id} إلى {end_id}\n"
            f"✅ تم سحب: {success_count} منشور\n"
            f"❌ فشل: {failed_count} منشور\n"
            f"📌 جميع المنشورات محفوظة بشكل دائم**",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف رسالة الحالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.MARKDOWN)

    except Exception as e:
        await status_msg.edit_text(
            f"**❌ حدث خطأ في سحب النطاق**\n`{str(e)}`",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.MARKDOWN)
        logging.error(f"Range download error: {e}")

    finally:
        active_downloads.pop(user_id, None)


async def handle_sequential_download(client, message, channel_username, start_message_id, direction):
    """معالجة السحب التسلسلي (للأمام أو للخلف)"""
    stop_button = InlineKeyboardMarkup([[
        InlineKeyboardButton("⏹️ إيقاف السحب", callback_data="stop_download")
    ]])

    direction_text = "للأمام ⬆️" if direction == "forward" else "للخلف ⬇️"
    status_msg = await message.reply_text(
        f"**⏳ جاري بدء السحب التسلسلي {direction_text}...**",
        reply_markup=stop_button,
        parse_mode=enums.ParseMode.MARKDOWN)

    try:
        user_id = message.from_user.id
        active_downloads[user_id] = True
        success_count = 0
        failed_count = 0
        consecutive_fails = 0
        current_id = start_message_id

        while active_downloads.get(user_id, True) and consecutive_fails < 50:
            try:
                msg = await client.get_messages(channel_username, current_id)
                if msg and not msg.empty:
                    # إرسال المنشور مع جعل النص عريضاً
                    await send_message_with_bold_text(client, msg, message.chat.id)
                    success_count += 1
                    consecutive_fails = 0

                    # فترة راحة كل 95 منشور
                    if success_count % 95 == 0:
                        # التحقق من الاشتراك أثناء فترة الراحة
                        not_subscribed = await check_user_subscription(client, user_id)
                        if not_subscribed:
                            await status_msg.edit_text(
                                "**❌ تم إيقاف العملية\n"
                                "🔒 يجب الاشتراك في جميع القنوات للمتابعة\n"
                                "📢 تحقق من اشتراكك وأعد المحاولة**",
                                parse_mode=enums.ParseMode.MARKDOWN)
                            active_downloads[user_id] = False
                            return

                        # عداد تنازلي لمدة دقيقتين (60 ثانية)
                        for remaining_time in range(60, 0, -1):
                            minutes = remaining_time // 60
                            seconds = remaining_time % 60
                            await status_msg.edit_text(
                                f"**⏸️ فترة راحة للحماية من الحظر\n"
                                f"✅ تم سحب: {success_count} منشور\n"
                                f"❌ فشل: {failed_count} منشور\n"
                                f"⏰ الوقت المتبقي: {minutes:02d}:{seconds:02d}\n"
                                f"🔄 سيتم استكمال العملية تلقائياً**",
                                reply_markup=stop_button,
                                parse_mode=enums.ParseMode.MARKDOWN)
                            await asyncio.sleep(1)

                            # التحقق من إيقاف العملية أثناء الانتظار
                            if not active_downloads.get(user_id, True):
                                return

                    await status_msg.edit_text(
                        f"**⏳ جاري السحب التسلسلي {direction_text}...\n"
                        f"✅ تم سحب: {success_count} منشور\n"
                        f"❌ فشل: {failed_count} منشور\n"
                        f"🔄 جاري معالجة المنشور رقم: {current_id}**",
                        reply_markup=stop_button,
                        parse_mode=enums.ParseMode.MARKDOWN)
                else:
                    # الرسالة محذوفة أو فارغة - نتخطاها ولا نعتبرها فشل متتالي
                    failed_count += 1
                    # لا نزيد consecutive_fails هنا لأن الرسالة قد تكون محذوفة فقط
            except Exception as e:
                failed_count += 1
                error_msg = str(e).lower()
                if "username_invalid" in error_msg:
                    # إذا كان اسم المستخدم غير صحيح، أوقف العملية
                    await status_msg.edit_text(
                        f"<b>❌ تم إيقاف العملية</b>\n\n"
                        f"<b>السبب:</b> اسم القناة غير صحيح أو غير موجود\n"
                        f"<b>القناة:</b> @{channel_username}\n\n"
                        f"<b>✅ تم سحب:</b> {success_count} منشور\n"
                        f"<b>❌ فشل:</b> {failed_count} منشور",
                        reply_markup=InlineKeyboardMarkup([[
                            InlineKeyboardButton("❌ حذف الرسالة",
                                                 callback_data="delete_now")
                        ]]),
                        parse_mode=enums.ParseMode.HTML)
                    active_downloads[user_id] = False
                    return
                elif "message_id_invalid" not in error_msg and "message_delete_forbidden" not in error_msg:
                    # فقط نزيد consecutive_fails للأخطاء الحقيقية، ليس للرسائل المحذوفة
                    consecutive_fails += 1
                    logging.error(f"Error processing message {current_id}: {e}")

            # تحديد الاتجاه
            if direction == "forward":
                current_id += 1
            else:
                current_id -= 1

            await asyncio.sleep(3)  # 3 ثوان للحماية القصوى من الحظر

        await status_msg.edit_text(
            f"**✅ اكتملت عملية السحب التسلسلي {direction_text}\n"
            f"✅ تم سحب: {success_count} منشور\n"
            f"❌ فشل: {failed_count} منشور\n"
            f"📌 جميع المنشورات محفوظة بشكل دائم**",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف رسالة الحالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.MARKDOWN)

    except Exception as e:
        await status_msg.edit_text(
            f"**❌ حدث خطأ في السحب التسلسلي**\n`{str(e)}`",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("❌ حذف الرسالة",
                                     callback_data="delete_now")
            ]]),
            parse_mode=enums.ParseMode.MARKDOWN)
        logging.error(f"Sequential download error: {e}")

    finally:
        active_downloads.pop(user_id, None)
        user_states.pop(user_id, None)


async def check_expired_memberships_periodically():
    """فحص العضويات المنتهية كل ساعة"""
    while True:
        try:
            expired_count = user_db.check_all_expired_memberships()
            if expired_count > 0:
                logger.info(f"تم إلغاء {expired_count} عضوية منتهية تلقائياً")

            # انتظار ساعة واحدة
            await asyncio.sleep(3600)
        except Exception as e:
            logger.error(f"خطأ في فحص العضويات المنتهية: {e}")
            await asyncio.sleep(300)  # انتظار 5 دقائق في حالة الخطأ


async def startup_tasks():
    """مهام بدء التشغيل"""
    try:
        # فحص العضويات المنتهية عند بدء التشغيل
        expired_count = user_db.check_all_expired_memberships()
        logger.info(f"تم فحص العضويات عند بدء التشغيل - تم إلغاء {expired_count} عضوية منتهية")

        # بدء مهمة فحص العضويات الدورية
        asyncio.create_task(check_expired_memberships_periodically())

        logger.info("تم بدء جميع المهام بنجاح")
    except Exception as e:
        logger.error(f"خطأ في مهام بدء التشغيل: {e}")


async def main():
    await app.start()

    # تشغيل مهام بدء التشغيل
    await startup_tasks()

    print("Bot is running...")
    logger.info("🤖 البوت يعمل بنجاح مع نظام VIP المتطور!")

    # استخدام حلقة لا نهائية للحفاظ على البوت قيد التشغيل
    while True:
        await asyncio.sleep(1)


if __name__ == "__main__":
    try:
        app.run(main())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user!")
        app.stop()
    except Exception as e:
        logger.error(f"Critical error: {e}")




